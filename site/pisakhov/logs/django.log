INFO 2025-06-05 21:52:55,939 autoreload 79688 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 21:53:01,098 basehttp 79688 6126432256 "GET /gobeyond/ HTTP/1.1" 200 277756
INFO 2025-06-05 21:53:01,170 basehttp 79688 6143258624 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-05 21:53:01,177 basehttp 79688 6126432256 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-05 21:53:01,184 basehttp 79688 6126432256 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-05 21:55:24,287 autoreload 79688 8619180608 /Users/<USER>/Library/CloudStorage/GoogleDrive-pisa<PERSON><PERSON>@gmail.com/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 21:55:25,121 autoreload 80394 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 21:55:30,364 autoreload 80394 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 21:55:31,020 autoreload 80430 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 21:57:56,047 basehttp 80430 6136311808 "GET /gobeyond/ HTTP/1.1" 200 277756
INFO 2025-06-05 21:57:56,077 basehttp 80430 6136311808 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-05 21:57:56,078 basehttp 80430 6153138176 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-05 21:57:56,100 basehttp 80430 6153138176 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-05 21:58:04,208 basehttp 80430 6136311808 "POST /gobeyond/predict_goal/ HTTP/1.1" 200 14
INFO 2025-06-05 21:58:05,074 basehttp 80430 6136311808 "POST /gobeyond/fix_grammar/ HTTP/1.1" 200 28
INFO 2025-06-05 21:58:10,541 basehttp 80430 6136311808 "POST /gobeyond/upload_audio/ HTTP/1.1" 200 39
INFO 2025-06-05 21:58:11,565 basehttp 80430 6136311808 "POST /gobeyond/predict_goal/ HTTP/1.1" 200 14
INFO 2025-06-05 21:58:20,968 basehttp 80430 6136311808 "GET / HTTP/1.1" 200 32374
INFO 2025-06-05 22:00:12,208 autoreload 80430 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/models.py changed, reloading.
INFO 2025-06-05 22:00:13,050 autoreload 83289 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:00:21,352 autoreload 83289 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/models.py changed, reloading.
INFO 2025-06-05 22:00:21,957 autoreload 83339 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:00:30,923 autoreload 83339 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/models.py changed, reloading.
INFO 2025-06-05 22:00:31,548 autoreload 83396 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:01:12,573 autoreload 83396 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:01:13,205 autoreload 83663 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:01:21,188 autoreload 83663 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:01:21,760 autoreload 83706 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:01:30,541 autoreload 83706 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/urls.py changed, reloading.
INFO 2025-06-05 22:01:31,158 autoreload 83759 8619180608 Watching for file changes with StatReloader
ERROR 2025-06-05 22:01:34,865 log 83759 6128709632 Internal Server Error: /gobeyond/
Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/contrib/auth/decorators.py", line 60, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py", line 164, in home_gobeyond
    return render(request, 'home_gobeyond.html', {'goals': goals, 'show_archived': show_archived})
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/backends/django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 969, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 969, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 969, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 969, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/urls/base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/urls/resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'delete_goal' not found. 'delete_goal' is not a valid view function or pattern name.
ERROR 2025-06-05 22:01:34,868 basehttp 83759 6128709632 "GET /gobeyond/ HTTP/1.1" 500 182353
ERROR 2025-06-05 22:02:27,874 log 83759 6128709632 Internal Server Error: /gobeyond/
Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/contrib/auth/decorators.py", line 60, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py", line 164, in home_gobeyond
    return render(request, 'home_gobeyond.html', {'goals': goals, 'show_archived': show_archived})
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/backends/django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 969, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 969, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 969, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 969, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/urls/base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/urls/resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'delete_goal' not found. 'delete_goal' is not a valid view function or pattern name.
ERROR 2025-06-05 22:02:27,879 basehttp 83759 6128709632 "GET /gobeyond/ HTTP/1.1" 500 184073
ERROR 2025-06-05 22:02:28,866 log 83759 6128709632 Internal Server Error: /gobeyond/
Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/contrib/auth/decorators.py", line 60, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py", line 164, in home_gobeyond
    return render(request, 'home_gobeyond.html', {'goals': goals, 'show_archived': show_archived})
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/backends/django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 969, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 969, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 969, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 969, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/urls/base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/urls/resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'delete_goal' not found. 'delete_goal' is not a valid view function or pattern name.
ERROR 2025-06-05 22:02:28,868 basehttp 83759 6128709632 "GET /gobeyond/ HTTP/1.1" 500 184073
ERROR 2025-06-05 22:02:30,547 log 83759 6128709632 Internal Server Error: /gobeyond/
Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/contrib/auth/decorators.py", line 60, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py", line 164, in home_gobeyond
    return render(request, 'home_gobeyond.html', {'goals': goals, 'show_archived': show_archived})
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/backends/django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 969, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 969, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 1008, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 969, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/base.py", line 969, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/template/defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/urls/base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/urls/resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'delete_goal' not found. 'delete_goal' is not a valid view function or pattern name.
ERROR 2025-06-05 22:02:30,549 basehttp 83759 6128709632 "GET /gobeyond/ HTTP/1.1" 500 183929
INFO 2025-06-05 22:03:04,612 autoreload 83759 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/admin.py changed, reloading.
INFO 2025-06-05 22:03:05,312 autoreload 84165 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:03:09,179 basehttp 84165 6137114624 "GET /gobeyond/ HTTP/1.1" 200 279112
INFO 2025-06-05 22:03:09,213 basehttp 84165 6137114624 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-05 22:03:09,215 basehttp 84165 6153940992 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-05 22:03:09,231 basehttp 84165 6153940992 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-05 22:03:12,983 basehttp 84165 6153940992 "GET /gobeyond/?archived=true HTTP/1.1" 200 13594
INFO 2025-06-05 22:03:14,783 basehttp 84165 6153940992 "GET /gobeyond/ HTTP/1.1" 200 279112
INFO 2025-06-05 22:03:25,339 basehttp 84165 6153940992 "GET /dashboard HTTP/1.1" 301 0
INFO 2025-06-05 22:03:25,414 basehttp 84165 6137114624 "GET /dashboard/ HTTP/1.1" 200 320173
INFO 2025-06-05 22:03:25,618 basehttp 84165 6137114624 "GET /dashboard/notifications/ HTTP/1.1" 200 21
INFO 2025-06-05 22:03:26,242 basehttp 84165 6137114624 "GET /admin HTTP/1.1" 301 0
INFO 2025-06-05 22:03:26,269 basehttp 84165 6153940992 "GET /admin/ HTTP/1.1" 200 11292
INFO 2025-06-05 22:03:26,306 basehttp 84165 6187593728 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-06-05 22:03:26,306 basehttp 84165 6153940992 "GET /static/admin/css/base.css HTTP/1.1" 200 22092
INFO 2025-06-05 22:03:26,306 basehttp 84165 6170767360 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
INFO 2025-06-05 22:03:26,308 basehttp 84165 6221246464 "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
INFO 2025-06-05 22:03:26,308 basehttp 84165 6204420096 "GET /static/admin/css/responsive.css HTTP/1.1" 200 17972
INFO 2025-06-05 22:03:26,309 basehttp 84165 6136541184 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2804
INFO 2025-06-05 22:03:26,319 basehttp 84165 6136541184 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
INFO 2025-06-05 22:03:26,322 basehttp 84165 6136541184 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
INFO 2025-06-05 22:03:26,322 basehttp 84165 6204420096 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
INFO 2025-06-05 22:03:26,323 basehttp 84165 6221246464 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-06-05 22:03:29,520 basehttp 84165 6221246464 "GET /admin/gobeyond/goal/ HTTP/1.1" 200 18839
INFO 2025-06-05 22:03:29,568 basehttp 84165 6221246464 "GET /static/admin/css/changelists.css HTTP/1.1" 200 6878
INFO 2025-06-05 22:03:29,578 basehttp 84165 6204420096 "GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
INFO 2025-06-05 22:03:29,583 basehttp 84165 6153940992 "GET /static/admin/js/actions.js HTTP/1.1" 200 8076
INFO 2025-06-05 22:03:29,585 basehttp 84165 6170767360 "GET /static/admin/js/core.js HTTP/1.1" 200 6208
INFO 2025-06-05 22:03:29,586 basehttp 84165 6187593728 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 9097
INFO 2025-06-05 22:03:29,586 basehttp 84165 6204420096 "GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
INFO 2025-06-05 22:03:29,587 basehttp 84165 6153940992 "GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
INFO 2025-06-05 22:03:29,589 basehttp 84165 6221246464 "GET /admin/jsi18n/ HTTP/1.1" 200 3342
INFO 2025-06-05 22:03:29,590 basehttp 84165 6204420096 "GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
INFO 2025-06-05 22:03:29,591 basehttp 84165 6187593728 "GET /static/admin/img/search.svg HTTP/1.1" 200 458
INFO 2025-06-05 22:03:29,593 basehttp 84165 6136541184 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 285314
INFO 2025-06-05 22:03:29,594 basehttp 84165 6170767360 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 325171
INFO 2025-06-05 22:03:29,602 basehttp 84165 6170767360 "GET /static/admin/js/filters.js HTTP/1.1" 200 978
INFO 2025-06-05 22:03:29,612 basehttp 84165 6170767360 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
INFO 2025-06-05 22:03:29,613 basehttp 84165 6136541184 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
INFO 2025-06-05 22:03:36,993 basehttp 84165 6136541184 "GET /admin/gobeyond/goal/1/change/ HTTP/1.1" 200 31805
INFO 2025-06-05 22:03:37,006 basehttp 84165 6136541184 "GET /static/admin/css/forms.css HTTP/1.1" 200 8794
INFO 2025-06-05 22:03:37,018 basehttp 84165 6187593728 "GET /static/admin/js/calendar.js HTTP/1.1" 200 9141
INFO 2025-06-05 22:03:37,019 basehttp 84165 6136541184 "GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 200 19319
INFO 2025-06-05 22:03:37,019 basehttp 84165 6204420096 "GET /static/admin/js/inlines.js HTTP/1.1" 200 15526
INFO 2025-06-05 22:03:37,020 basehttp 84165 6153940992 "GET /static/admin/css/widgets.css HTTP/1.1" 200 11564
INFO 2025-06-05 22:03:37,021 basehttp 84165 6221246464 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
INFO 2025-06-05 22:03:37,021 basehttp 84165 6170767360 "GET /admin/jsi18n/ HTTP/1.1" 200 3342
INFO 2025-06-05 22:03:37,037 basehttp 84165 6221246464 "GET /static/admin/js/change_form.js HTTP/1.1" 200 606
INFO 2025-06-05 22:03:37,104 basehttp 84165 6221246464 "GET /static/admin/img/icon-calendar.svg HTTP/1.1" 200 1086
INFO 2025-06-05 22:03:44,401 basehttp 84165 6221246464 "GET /admin/gobeyond/goal/ HTTP/1.1" 200 18839
INFO 2025-06-05 22:03:44,422 basehttp 84165 6221246464 "GET /admin/jsi18n/ HTTP/1.1" 200 3342
INFO 2025-06-05 22:03:45,594 basehttp 84165 6221246464 "GET /admin/gobeyond/ HTTP/1.1" 200 5735
INFO 2025-06-05 22:03:46,854 basehttp 84165 6221246464 "GET /admin/ HTTP/1.1" 200 11155
INFO 2025-06-05 22:03:48,610 basehttp 84165 6221246464 "GET /admin/gobeyond/record/ HTTP/1.1" 200 46602
INFO 2025-06-05 22:03:48,627 basehttp 84165 6221246464 "GET /admin/jsi18n/ HTTP/1.1" 200 3342
INFO 2025-06-05 22:03:56,004 basehttp 84165 6221246464 "GET /admin/gobeyond/ HTTP/1.1" 200 5735
INFO 2025-06-05 22:04:23,731 autoreload 84165 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:04:24,416 autoreload 84510 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:05:25,871 autoreload 84886 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:05:35,463 basehttp 84510 6156824576 "GET / HTTP/1.1" 200 32374
INFO 2025-06-05 22:05:37,676 basehttp 84510 6156824576 "GET /gobeyond/ HTTP/1.1" 200 279112
INFO 2025-06-05 22:05:37,699 basehttp 84510 6156824576 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-05 22:05:37,701 basehttp 84510 6156824576 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-05 22:05:37,716 basehttp 84510 6156824576 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-05 22:05:39,475 basehttp 84510 6156824576 "GET /gobeyond/?archived=true HTTP/1.1" 200 13891
INFO 2025-06-05 22:05:40,468 basehttp 84510 6156824576 "GET /gobeyond/ HTTP/1.1" 200 279112
INFO 2025-06-05 22:06:31,847 basehttp 84510 6156824576 "GET /gobeyond/ HTTP/1.1" 200 279112
INFO 2025-06-05 22:06:35,728 basehttp 84510 6156824576 "GET /gobeyond/ HTTP/1.1" 200 279112
WARNING 2025-06-05 22:06:40,640 log 84510 6156824576 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-05 22:06:40,642 basehttp 84510 6156824576 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-05 22:06:42,839 basehttp 84510 6156824576 "GET /gobeyond/ HTTP/1.1" 200 279112
WARNING 2025-06-05 22:06:42,916 log 84510 6156824576 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-05 22:06:42,917 basehttp 84510 6156824576 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-05 22:06:42,928 basehttp 84510 6173650944 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-05 22:06:42,933 basehttp 84510 6173650944 "GET /static/js/utils.js HTTP/1.1" 200 5217
INFO 2025-06-05 22:06:42,969 basehttp 84510 6173650944 "GET /static/js/dialog_manager.js HTTP/1.1" 200 2606
INFO 2025-06-05 22:06:42,970 basehttp 84510 6156824576 "GET /static/js/add_record.js HTTP/1.1" 200 3342
INFO 2025-06-05 22:06:42,999 basehttp 84510 6156824576 "GET /static/js/goal_management.js HTTP/1.1" 200 3089
INFO 2025-06-05 22:06:42,999 basehttp 84510 6173650944 "GET /static/js/media_handling.js HTTP/1.1" 200 7603
INFO 2025-06-05 22:06:45,299 basehttp 84510 6173650944 "POST /gobeyond/archive_goal/4/ HTTP/1.1" 302 0
INFO 2025-06-05 22:06:45,321 basehttp 84510 6173650944 "GET /gobeyond/ HTTP/1.1" 200 204281
WARNING 2025-06-05 22:06:45,356 log 84510 6173650944 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-05 22:06:45,359 basehttp 84510 6173650944 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-05 22:06:45,366 basehttp 84510 6156824576 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-05 22:06:45,370 basehttp 84510 6156824576 "GET /static/js/utils.js HTTP/1.1" 200 5217
INFO 2025-06-05 22:06:45,370 basehttp 84510 6173650944 "GET /static/js/dialog_manager.js HTTP/1.1" 200 2606
INFO 2025-06-05 22:06:45,415 basehttp 84510 6173650944 "GET /static/js/add_record.js HTTP/1.1" 200 3342
INFO 2025-06-05 22:06:45,415 basehttp 84510 6156824576 "GET /static/js/media_handling.js HTTP/1.1" 200 7603
INFO 2025-06-05 22:06:45,440 basehttp 84510 6156824576 "GET /static/js/goal_management.js HTTP/1.1" 200 3089
INFO 2025-06-05 22:06:47,901 basehttp 84510 6156824576 "GET /gobeyond/?archived=true HTTP/1.1" 200 88490
WARNING 2025-06-05 22:06:47,947 log 84510 6156824576 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-05 22:06:47,953 basehttp 84510 6156824576 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-05 22:06:47,955 basehttp 84510 6173650944 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-05 22:06:47,979 basehttp 84510 6156824576 "GET /static/js/dialog_manager.js HTTP/1.1" 200 2606
INFO 2025-06-05 22:06:47,980 basehttp 84510 6173650944 "GET /static/js/utils.js HTTP/1.1" 200 5217
INFO 2025-06-05 22:06:48,004 basehttp 84510 6156824576 "GET /static/js/media_handling.js HTTP/1.1" 200 7603
INFO 2025-06-05 22:06:48,005 basehttp 84510 6173650944 "GET /static/js/add_record.js HTTP/1.1" 200 3342
INFO 2025-06-05 22:06:48,034 basehttp 84510 6156824576 "GET /static/js/goal_management.js HTTP/1.1" 200 3089
INFO 2025-06-05 22:06:49,616 basehttp 84510 6156824576 "GET /gobeyond/ HTTP/1.1" 200 204281
WARNING 2025-06-05 22:06:49,671 log 84510 6173650944 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-05 22:06:49,672 basehttp 84510 6173650944 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-05 22:06:49,674 basehttp 84510 6156824576 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-05 22:06:49,680 basehttp 84510 6156824576 "GET /static/js/utils.js HTTP/1.1" 200 5217
INFO 2025-06-05 22:06:49,685 basehttp 84510 6156824576 "GET /static/js/dialog_manager.js HTTP/1.1" 200 2606
INFO 2025-06-05 22:06:49,723 basehttp 84510 6156824576 "GET /static/js/add_record.js HTTP/1.1" 200 3342
INFO 2025-06-05 22:06:49,724 basehttp 84510 6173650944 "GET /static/js/media_handling.js HTTP/1.1" 200 7603
INFO 2025-06-05 22:06:49,750 basehttp 84510 6173650944 "GET /static/js/goal_management.js HTTP/1.1" 200 3089
INFO 2025-06-05 22:06:52,195 basehttp 84510 6173650944 "POST /gobeyond/archive_goal/3/ HTTP/1.1" 302 0
INFO 2025-06-05 22:06:52,212 basehttp 84510 6173650944 "GET /gobeyond/ HTTP/1.1" 200 105783
WARNING 2025-06-05 22:06:52,270 log 84510 6173650944 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-05 22:06:52,270 basehttp 84510 6173650944 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-05 22:06:52,271 basehttp 84510 6156824576 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-05 22:06:52,280 basehttp 84510 6156824576 "GET /static/js/utils.js HTTP/1.1" 200 5217
INFO 2025-06-05 22:06:52,293 basehttp 84510 6156824576 "GET /static/js/dialog_manager.js HTTP/1.1" 200 2606
INFO 2025-06-05 22:06:52,325 basehttp 84510 6156824576 "GET /static/js/add_record.js HTTP/1.1" 200 3342
INFO 2025-06-05 22:06:52,326 basehttp 84510 6173650944 "GET /static/js/media_handling.js HTTP/1.1" 200 7603
INFO 2025-06-05 22:06:52,354 basehttp 84510 6173650944 "GET /static/js/goal_management.js HTTP/1.1" 200 3089
INFO 2025-06-05 22:06:55,134 basehttp 84510 6173650944 "POST /gobeyond/archive_goal/2/ HTTP/1.1" 302 0
INFO 2025-06-05 22:06:55,147 basehttp 84510 6173650944 "GET /gobeyond/ HTTP/1.1" 200 13955
WARNING 2025-06-05 22:06:55,215 log 84510 6173650944 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-05 22:06:55,215 basehttp 84510 6173650944 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-05 22:06:55,217 basehttp 84510 6156824576 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-05 22:06:55,224 basehttp 84510 6156824576 "GET /static/js/utils.js HTTP/1.1" 200 5217
INFO 2025-06-05 22:06:55,224 basehttp 84510 6173650944 "GET /static/js/dialog_manager.js HTTP/1.1" 200 2606
INFO 2025-06-05 22:06:55,260 basehttp 84510 6156824576 "GET /static/js/add_record.js HTTP/1.1" 200 3342
INFO 2025-06-05 22:06:55,260 basehttp 84510 6173650944 "GET /static/js/media_handling.js HTTP/1.1" 200 7603
INFO 2025-06-05 22:06:55,285 basehttp 84510 6173650944 "GET /static/js/goal_management.js HTTP/1.1" 200 3089
INFO 2025-06-05 22:07:04,766 basehttp 84510 6173650944 "POST /gobeyond/predict_goal/ HTTP/1.1" 200 14
INFO 2025-06-05 22:07:05,168 basehttp 84510 6156824576 "POST /gobeyond/fix_grammar/ HTTP/1.1" 200 28
INFO 2025-06-05 22:07:09,783 basehttp 84510 6156824576 "POST /gobeyond/upload_audio/ HTTP/1.1" 200 39
INFO 2025-06-05 22:07:10,806 basehttp 84510 6156824576 "POST /gobeyond/predict_goal/ HTTP/1.1" 200 14
INFO 2025-06-05 22:07:20,992 basehttp 84510 6156824576 "POST /gobeyond/upload_audio/ HTTP/1.1" 200 39
INFO 2025-06-05 22:07:22,018 basehttp 84510 6156824576 "POST /gobeyond/predict_goal/ HTTP/1.1" 200 14
INFO 2025-06-05 22:12:58,625 basehttp 84510 6173650944 "GET /gobeyond/ HTTP/1.1" 200 13955
INFO 2025-06-05 22:12:58,675 basehttp 84510 6173650944 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-05 22:12:58,675 basehttp 84510 12901707776 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-05 22:12:58,683 basehttp 84510 12901707776 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-05 22:12:58,709 basehttp 84510 12901707776 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-05 22:12:58,737 basehttp 84510 12901707776 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-05 22:12:59,692 basehttp 84510 12918534144 "GET /gobeyond/?archived=true HTTP/1.1" 200 278334
INFO 2025-06-05 22:13:02,613 basehttp 84510 12918534144 "POST /gobeyond/unarchive_goal/4/ HTTP/1.1" 302 0
INFO 2025-06-05 22:13:02,629 basehttp 84510 12918534144 "GET /gobeyond/ HTTP/1.1" 200 88786
WARNING 2025-06-05 22:13:22,169 log 84510 6156824576 Not Found: /favicon.ico
WARNING 2025-06-05 22:13:22,170 basehttp 84510 6156824576 "GET /favicon.ico HTTP/1.1" 404 6399
INFO 2025-06-05 22:13:26,818 autoreload 84510 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/models.py changed, reloading.
INFO 2025-06-05 22:13:27,767 autoreload 87289 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:13:51,848 autoreload 87289 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:13:52,589 autoreload 87452 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:14:09,360 autoreload 87452 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:14:10,053 autoreload 87547 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:14:25,638 autoreload 87547 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:14:26,273 autoreload 87642 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:14:35,337 autoreload 87642 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:14:35,905 autoreload 87677 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:14:44,799 autoreload 87677 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:14:45,399 autoreload 87721 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:14:55,398 autoreload 87721 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:14:55,997 autoreload 87763 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:15:12,340 autoreload 87763 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:15:12,953 autoreload 87834 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:15:23,070 autoreload 87834 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/urls.py changed, reloading.
INFO 2025-06-05 22:15:23,685 autoreload 87880 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:15:32,731 autoreload 87880 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/admin.py changed, reloading.
INFO 2025-06-05 22:15:33,331 autoreload 87919 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:15:41,068 autoreload 87919 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/admin.py changed, reloading.
INFO 2025-06-05 22:15:41,655 autoreload 87956 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:15:53,610 autoreload 87956 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/admin.py changed, reloading.
INFO 2025-06-05 22:15:54,187 autoreload 88007 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:18:14,891 basehttp 88007 6193262592 "GET /gobeyond/ HTTP/1.1" 200 89844
INFO 2025-06-05 22:18:14,914 basehttp 88007 6193262592 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-05 22:18:14,919 basehttp 88007 6193262592 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-05 22:18:14,927 basehttp 88007 6210088960 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-05 22:18:14,927 basehttp 88007 6193262592 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-05 22:18:14,968 basehttp 88007 6210088960 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-05 22:18:14,970 basehttp 88007 6193262592 "GET /static/js/milestone_tracker.js HTTP/1.1" 200 9980
INFO 2025-06-05 22:19:15,750 basehttp 88007 6193262592 "GET /gobeyond/ HTTP/1.1" 200 89844
INFO 2025-06-05 22:19:15,794 basehttp 88007 6193262592 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 22:19:23,311 basehttp 88007 6193262592 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
ERROR 2025-06-05 22:19:29,667 views 88007 6193262592 Failed to parse AI milestone response: Here is the generated milestone tracking plan:

```
{
  "milestones": [
    {
      "title": "Establish Tableau Foundation",
      "description": "Complete setup of Tableau access, onboard team members, and establish a GitHub repository for collaborative work",
      "order": 1
    },
    {
      "title": "Automate Reporting and Analytics",
      "description": "Automate weekly reports, connect data directly to Tableau, and create auto-refresh reports to reduce manual tasks",
      "order": 2
    },
    {
      "title": "Develop Performance MIS Dashboard",
      "description": "Complete Phase Two of the Performance MIS dashboard in Tableau, enabling tracking of key performance indicators",
      "order": 3
    },
    {
      "title": "Scale Tableau Adoption",
      "description": "Onboard remaining team members to Tableau, provide training and support, and ensure seamless integration with GitHub repository",
      "order": 4
    }
  ],
  "next_suggested": "Focus on completing Phase Two of the Performance MIS dashboard in Tableau to further enhance analytics capabilities",
  "confidence": 0.92
}
```

This plan breaks down the goal into achievable milestones, considering the existing progress records. The next suggested milestone is based on the current progress, focusing on completing the Performance MIS dashboard to further enhance analytics capabilities.
INFO 2025-06-05 22:19:29,679 basehttp 88007 6193262592 "POST /gobeyond/regenerate_milestones/4/ HTTP/1.1" 200 77
INFO 2025-06-05 22:19:29,687 basehttp 88007 6193262592 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 933
ERROR 2025-06-05 22:20:57,193 views 88007 6193262592 Failed to parse AI milestone response: Here is the generated milestone tracking plan:

{
  "milestones": [
    {
      "title": "Establish Tableau Access and Onboarding",
      "description": "Set up LDAP groups, onboard team members, and provide access to Tableau for seamless report organization and automation",
      "order": 1
    },
    {
      "title": "Develop Core Dashboards and Reports",
      "description": "Create key performance indicator dashboards, payment analysis, and risk trend reports to demonstrate the value of Tableau in business growth and risk analysis",
      "order": 2
    },
    {
      "title": "Automate Manual Processes and Integrate with GitHub",
      "description": "Automate weekly reports, eliminate manual tasks, and integrate Tableau with GitHub for collaborative project management and version control",
      "order": 3
    },
    {
      "title": "Scale Adoption and Refine Analytics",
      "description": "Expand Tableau adoption across the team, refine analytics capabilities, and identify opportunities for business growth and risk mitigation",
      "order": 4
    }
  ],
  "next_suggested": "Develop a comprehensive training plan to ensure all team members are proficient in using Tableau and GitHub, and schedule regular check-ins to monitor progress and address any challenges",
  "confidence": 0.92
}
INFO 2025-06-05 22:20:57,200 basehttp 88007 6193262592 "POST /gobeyond/regenerate_milestones/4/ HTTP/1.1" 200 77
INFO 2025-06-05 22:20:57,206 basehttp 88007 6193262592 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 933
INFO 2025-06-05 22:21:17,217 basehttp 88007 6210088960 "POST /gobeyond/complete_milestone/4/4/ HTTP/1.1" 200 64
INFO 2025-06-05 22:21:17,228 basehttp 88007 6210088960 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 962
INFO 2025-06-05 22:21:18,597 basehttp 88007 6210088960 "POST /gobeyond/complete_milestone/4/5/ HTTP/1.1" 200 64
INFO 2025-06-05 22:21:18,606 basehttp 88007 6210088960 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 991
INFO 2025-06-05 22:21:21,056 basehttp 88007 6226915328 "POST /gobeyond/complete_milestone/4/6/ HTTP/1.1" 200 64
INFO 2025-06-05 22:21:21,062 basehttp 88007 6226915328 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 1021
ERROR 2025-06-05 22:21:24,641 views 88007 6226915328 Failed to parse AI milestone response: Here is the generated milestone tracking plan:

```
{
  "milestones": [
    {
      "title": "Establish Foundation for Tableau Adoption",
      "description": "Complete setup of LDAP groups, onboard team members, and introduce GitHub repository for collaborative work",
      "order": 1
    },
    {
      "title": "Automate Reporting and Analytics",
      "description": "Develop and deploy automated reports using Tableau, including payment analysis and performance MIS dashboard",
      "order": 2
    },
    {
      "title": "Scale Tableau Adoption and Best Practices",
      "description": "Conduct training sessions for the entire team, establish bi-weekly meetings for progress tracking, and ensure seamless integration with GitHub repository",
      "order": 3
    },
    {
      "title": "Optimize Risk Analysis and Business Growth",
      "description": "Develop and refine risk trend analysis, company-to-industry comparisons, and identify opportunities for business growth using Tableau",
      "order": 4
    }
  ],
  "next_suggested": "Develop a comprehensive training plan for the entire team to ensure widespread adoption of Tableau and GitHub repository, focusing on best practices and advanced features",
  "confidence": 0.92
}
```

This plan breaks down the goal into achievable milestones, considering the existing progress records. The next suggested milestone is to develop a comprehensive training plan to ensure widespread adoption of Tableau and GitHub repository, which will help to scale the transition and achieve the goal by the deadline.
INFO 2025-06-05 22:21:24,653 basehttp 88007 6226915328 "POST /gobeyond/regenerate_milestones/4/ HTTP/1.1" 200 77
INFO 2025-06-05 22:21:24,662 basehttp 88007 6226915328 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 933
ERROR 2025-06-05 22:21:53,544 views 88007 6193262592 Failed to parse AI milestone response: Here is the generated milestone tracking plan:

```
{
  "milestones": [
    {
      "title": "Establish Tableau Foundation",
      "description": "Complete setup of Tableau access, onboard team members, and establish a collaborative workflow using GitHub repository.",
      "order": 1
    },
    {
      "title": "Automate Reporting and Analytics",
      "description": "Develop and deploy automated reports using Tableau, including payment analysis and performance MIS dashboard.",
      "order": 2
    },
    {
      "title": "Integrate Data Sources and Refine Risk Analysis",
      "description": "Connect data directly to Tableau, create auto-refresh reports, and refine risk trend analysis using Experian sandbox.",
      "order": 3
    },
    {
      "title": "Scale Automation and Adoption",
      "description": "Deploy automated weekly reports, expand Tableau adoption across the team, and ensure seamless integration with existing workflows.",
      "order": 4
    }
  ],
  "next_suggested": "Focus on integrating data sources and refining risk analysis to unlock the full potential of Tableau and drive business growth.",
  "confidence": 0.92
}
```

This plan breaks down the goal into achievable milestones, considering the existing progress records. The next suggested milestone is to integrate data sources and refine risk analysis, which will help unlock the full potential of Tableau and drive business growth.
INFO 2025-06-05 22:21:53,552 basehttp 88007 6193262592 "POST /gobeyond/edit_record/4/48/ HTTP/1.1" 302 0
INFO 2025-06-05 22:21:53,567 basehttp 88007 6193262592 "GET /gobeyond/ HTTP/1.1" 200 89846
INFO 2025-06-05 22:21:53,609 basehttp 88007 6193262592 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-05 22:21:53,669 basehttp 88007 6210088960 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-05 22:21:53,669 basehttp 88007 6193262592 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-05 22:21:53,686 basehttp 88007 6193262592 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 22:21:56,095 basehttp 88007 6226915328 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 936
ERROR 2025-06-05 22:21:59,452 views 88007 6226915328 Failed to parse AI milestone response: Here is the generated milestone tracking plan:

```
{
  "milestones": [
    {
      "title": "Establish Tableau Access and Onboarding",
      "description": "Set up LDAP groups, onboard team members to Tableau, and provide training on automation and report generation",
      "order": 1
    },
    {
      "title": "Develop Performance MIS Dashboard",
      "description": "Complete Phase Two of the Performance MIS dashboard in Tableau, integrating key performance indicators and automating report refresh",
      "order": 2
    },
    {
      "title": "Automate Weekly Reports and Integrate with GitHub",
      "description": "Deploy automated weekly reports through Tableau, and integrate with the GitHub repository for collaborative development and version control",
      "order": 3
    },
    {
      "title": "Conduct Company-to-Industry Analysis and Identify Risk Trends",
      "description": "Utilize the Experian sandbox to conduct company-to-industry analyses, identify risk trends, and provide actionable insights to stakeholders",
      "order": 4
    },
    {
      "title": "Full Transition to Tableau and Process Optimization",
      "description": "Complete the transition to Tableau, optimize business processes, and ensure seamless integration with existing workflows",
      "order": 5
    }
  ],
  "next_suggested": "Focus on completing Phase Two of the Performance MIS dashboard in Tableau, integrating key performance indicators and automating report refresh",
  "confidence": 0.85
}
```

This plan breaks down the goal into achievable milestones, considering the existing progress records. The next suggested milestone is to complete Phase Two of the Performance MIS dashboard, which builds upon the existing progress and sets the stage for further automation and integration with GitHub.
INFO 2025-06-05 22:21:59,458 basehttp 88007 6226915328 "POST /gobeyond/regenerate_milestones/4/ HTTP/1.1" 200 77
INFO 2025-06-05 22:21:59,466 basehttp 88007 6226915328 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 936
INFO 2025-06-05 22:22:08,334 autoreload 88007 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:22:09,149 autoreload 89635 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:22:21,640 autoreload 89635 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:22:22,246 autoreload 89698 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:22:30,211 autoreload 89698 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:22:30,705 autoreload 89747 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:22:48,850 autoreload 89747 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:22:49,516 autoreload 89843 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:23:06,177 basehttp 89843 6124040192 "GET /gobeyond/?archived=true HTTP/1.1" 200 205795
INFO 2025-06-05 22:23:06,208 basehttp 89843 6140866560 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-05 22:23:06,209 basehttp 89843 6124040192 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-05 22:23:06,231 basehttp 89843 6124040192 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 22:23:10,318 basehttp 89843 6124040192 "POST /gobeyond/unarchive_goal/2/ HTTP/1.1" 302 0
INFO 2025-06-05 22:23:10,328 basehttp 89843 6124040192 "GET /gobeyond/ HTTP/1.1" 200 182676
INFO 2025-06-05 22:23:13,234 basehttp 89843 6124040192 "GET /gobeyond/?archived=true HTTP/1.1" 200 113201
INFO 2025-06-05 22:23:15,658 basehttp 89843 6124040192 "POST /gobeyond/unarchive_goal/3/ HTTP/1.1" 302 0
INFO 2025-06-05 22:23:15,674 basehttp 89843 6124040192 "GET /gobeyond/ HTTP/1.1" 200 282176
INFO 2025-06-05 22:23:19,749 autoreload 89843 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:23:20,389 autoreload 89959 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:23:30,129 basehttp 89959 6155857920 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 936
INFO 2025-06-05 22:23:38,594 basehttp 89959 6155857920 "GET /gobeyond/ HTTP/1.1" 200 282176
INFO 2025-06-05 22:23:39,970 basehttp 89959 6155857920 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 120
INFO 2025-06-05 22:23:41,234 autoreload 89959 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:23:41,883 autoreload 90071 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:23:43,761 basehttp 90071 6206599168 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 936
INFO 2025-06-05 22:23:44,973 basehttp 90071 6206599168 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 115
ERROR 2025-06-05 22:23:48,394 views 90071 6206599168 Failed to parse AI milestone response: Here is the JSON object with the milestones and next suggested step:

```
{
  "milestones": [
    {
      "title": "Established Ad-Hoc Reporting for MIM and Loss Forecasting Teams",
      "description": "Created ad-hoc dashboards for MIM and Loss Forecasting teams, providing model score distributions and long-term forecasts",
      "order": 1,
      "is_completed": true,
      "completion_evidence": "Records 1 and 2"
    },
    {
      "title": "Enhanced Data Sharing and Collaboration",
      "description": "Provided account-level data to DM team and generated collection insights for delinquency performance",
      "order": 2,
      "is_completed": true,
      "completion_evidence": "Records 3 and 4"
    },
    {
      "title": "Identified and Addressed Critical Data Issues",
      "description": "Raised critical issue with data warehouses, identified non-payments, and developed code to identify auto payment issues",
      "order": 3,
      "is_completed": true,
      "completion_evidence": "Records 6, 12, and 13"
    },
    {
      "title": "Automated Reporting for Collection Metrics",
      "description": "Completed Phase 1 of collaboration with collection team, creating automated report in Tableau for collection metrics",
      "order": 4,
      "is_completed": true,
      "completion_evidence": "Record 7"
    },
    {
      "title": "Fostered Cross-Functional Collaboration",
      "description": "Assisted Risk Modeling Team in advocating for automation and collaborated with Loss Forecast team on reporting in Tableau",
      "order": 5,
      "is_completed": true,
      "completion_evidence": "Records 9 and 10"
    },
    {
      "title": "Resolved Critical Issues and Demonstrated Analytical Skills",
      "description": "Assisted in closing critical issue raised by Internal Audit and demonstrated analytical skills in resolving payment issues",
      "order": 6,
      "is_completed": true,
      "completion_evidence": "Record 11"
    },
    {
      "title": "Developed Advanced Analytical Capabilities",
      "description": "Wrote complicated logic for offshore consulting team and developed code for collection team",
      "order": 7,
      "is_completed": true,
      "completion_evidence": "Records 8 and 14"
    }
  ],
  "next_suggested": "Conduct a comprehensive review of inter-team collaboration progress and identify areas for further improvement and expansion",
  "confidence": 0.92
}
```

Note: The milestones are based on the actual progress records and reflect the work done in fostering relationships with other teams, enhancing analytical skills, and addressing concerns. The next suggested step is to review the progress and identify areas for further improvement and expansion.
INFO 2025-06-05 22:23:48,407 basehttp 90071 6206599168 "POST /gobeyond/regenerate_milestones/2/ HTTP/1.1" 200 77
INFO 2025-06-05 22:23:48,415 basehttp 90071 6206599168 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 938
INFO 2025-06-05 22:23:55,214 autoreload 90071 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:23:55,841 autoreload 90133 8619180608 Watching for file changes with StatReloader
ERROR 2025-06-05 22:24:01,924 views 90133 6172209152 Failed to parse AI milestone response: Here is the JSON object with the milestones and next suggested step:

```
{
  "milestones": [
    {
      "title": "Financial Risk Management Framework Development",
      "description": "Created a new framework, called models, to enhance financial risk management and facilitate more effective insights.",
      "order": 1,
      "is_completed": true,
      "completion_evidence": "Record 1: Pioneered a new framework, called models"
    },
    {
      "title": "Collection Analysis and Loan Assignment Proposal",
      "description": "Conducted collection analysis for a business proposal, answering independent risk questions and increasing the loan amount.",
      "order": 2,
      "is_completed": true,
      "completion_evidence": "Record 2: Created a collection analysis for a business proposal"
    },
    {
      "title": "Indicator Identification for Low FICO Band Improvement",
      "description": "Discovered a new indicator that correlates with charge-offs for our low FICO band, which can help improve our low FICO band.",
      "order": 3,
      "is_completed": true,
      "completion_evidence": "Record 3: Found a new indicator that correlates with charge-offs for our low FICO band"
    },
    {
      "title": "Performance Code Enhancement and Policy Alignment",
      "description": "Rewrote the entire current policy logic to enhance and align it with our current policy, enabling future policy enhancements.",
      "order": 4,
      "is_completed": true,
      "completion_evidence": "Record 5: Rewrote the entire current policy logic"
    },
    {
      "title": "Roll Rate Analysis and Loss Forecasting Report",
      "description": "Analyzed the roll rate for our portfolio, identifying its impact day over day using daily data, and contributed to a loss forecasting report.",
      "order": 5,
      "is_completed": true,
      "completion_evidence": "Record 6: Worked on an analysis to identify the roll rate for our portfolio"
    },
    {
      "title": "Data Quality Improvement and Field Overlayer Creation",
      "description": "Identified and resolved a critical issue where our data didn't capture a critical field in some dates, and created a new field overlayer.",
      "order": 6,
      "is_completed": true,
      "completion_evidence": "Record 7: Closed a critical issue where our data didn't capture a critical field"
    },
    {
      "title": "Credit Score Analysis for Policy Enhancement",
      "description": "Conducted an analysis to identify a new credit score for policy enhancement using our Experian Sandbox platform.",
      "order": 7,
      "is_completed": true,
      "completion_evidence": "Record 8: Conducted an analysis to identify a new credit score for policy enhancement"
    },
    {
      "title": "Return on Security Obligations (ROSO) Analysis",
      "description": "Analyzed the ROSO for our P2D segments, enabling the estimation of recovery from our deposits.",
      "order": 8,
      "is_completed": true,
      "completion_evidence": "Record 9: Conducted an analysis to identify the ROSO for our P2D segments"
    },
    {
      "title": "Credit Loss Analysis for Autopay Customers",
      "description": "Helped determine a credit loss for autopay customers.",
      "order": 9,
      "is_completed": true,
      "completion_evidence": "Record 10: Helped determine a credit loss for autopay customers"
    },
    {
      "title": "Customer Rejection Analysis and Code Development",
      "description": "Assisted a colleague in writing code to identify the reasons for customers' rejection of offers.",
      "order": 10,
      "is_completed": true,
      "completion_evidence": "Record 11: Assisted a colleague in writing code to identify the reasons for customers' rejection"
    },
    {
      "title": "Risk Trend Identification and Profile Analysis",
      "description": "Identified a risk trend and created a profile analysis, which was eventually highlighted in a Management Information Systems (MIS) report.",
      "order": 11,
      "is_completed": true,
      "completion_evidence": "Record 12: Created a profile analysis after identifying a risk trend"
    },
    {
      "title": "Cross-Product Underperformance Identification",
      "description": "Discovered a way to identify customers with cross-product underperformance that puts our portfolio at risk.",
      "order": 12,
      "is_completed": true,
      "completion_evidence": "Records 13-15: Discovered a way to identify customers with cross-product underperformance"
    }
  ],
  "next_suggested": "Integrate the newly developed models and indicators into the team's workflow and conduct a comprehensive
ERROR 2025-06-05 22:24:01,926 views 90133 6172209152 Error generating milestones with AI: name 'generate_fallback_milestones' is not defined
INFO 2025-06-05 22:24:01,947 basehttp 90133 6172209152 "POST /gobeyond/regenerate_milestones/3/ HTTP/1.1" 200 77
INFO 2025-06-05 22:24:01,976 basehttp 90133 6172209152 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 943
INFO 2025-06-05 22:24:13,345 autoreload 90133 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:24:14,009 autoreload 90237 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:24:22,980 autoreload 90237 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:24:23,560 autoreload 90273 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:24:28,536 basehttp 90273 6126399488 "GET /gobeyond/ HTTP/1.1" 200 282176
INFO 2025-06-05 22:24:28,564 basehttp 90273 6126399488 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-05 22:24:28,564 basehttp 90273 6143225856 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 22:24:32,991 basehttp 90273 ********** "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 938
ERROR 2025-06-05 22:24:35,932 views 90273 ********** Failed to parse AI milestone response: Here is the JSON object with the milestones and next suggested step:

```
{
  "milestones": [
    {
      "title": "Initial Ad-hoc Reporting and Data Sharing",
      "description": "Created ad-hoc dashboards for MIM and Loss and Forecasting ACL teams, and provided account-level data to DM team",
      "order": 1,
      "is_completed": true,
      "completion_evidence": "Records 1-3"
    },
    {
      "title": "Collaboration with Collection Team (Phase 1)",
      "description": "Created automated report in Tableau for collection metrics, aligning with official numbers and providing useful metrics for evaluating collection performance",
      "order": 2,
      "is_completed": true,
      "completion_evidence": "Record 7"
    },
    {
      "title": "Technical Support and Advocacy",
      "description": "Provided complicated logic for offshore consulting team, assisted Risk Modeling Team in advocating for automation, and wrote code for identifying auto payment issues",
      "order": 3,
      "is_completed": true,
      "completion_evidence": "Records 8-9, 12"
    },
    {
      "title": "Issue Resolution and Critical Issue Identification",
      "description": "Raised critical issue with data warehouses, assisted in closing critical issue raised by Internal Audit, and identified critical issue in data allowing for policy criteria modification",
      "order": 4,
      "is_completed": true,
      "completion_evidence": "Records 6, 11, 13"
    }
  ],
  "next_suggested": "Collaborate with Loss and Forecast team to develop advanced reporting in Tableau, building on the initial communication initiated",
  "confidence": 0.85
}
```

The milestones are based on the actual progress records, grouping related activities together. The next suggested step is a logical progression from the current progress, focusing on advancing the collaboration with the Loss and Forecast team.
INFO 2025-06-05 22:24:35,940 basehttp 90273 ********** "POST /gobeyond/regenerate_milestones/2/ HTTP/1.1" 200 77
INFO 2025-06-05 22:24:35,948 basehttp 90273 ********** "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 1001
INFO 2025-06-05 22:24:48,035 basehttp 90273 ********** "POST /gobeyond/complete_milestone/2/22/ HTTP/1.1" 200 64
INFO 2025-06-05 22:24:48,044 basehttp 90273 ********** "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 1030
INFO 2025-06-05 22:24:51,717 basehttp 90273 6142652416 "POST /gobeyond/complete_milestone/2/23/ HTTP/1.1" 200 64
INFO 2025-06-05 22:24:51,725 basehttp 90273 6142652416 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 1059
ERROR 2025-06-05 22:25:16,988 views 90505 8619180608 Failed to parse AI milestone response: Here is the JSON object with the milestones and next suggested step:

```
{
  "milestones": [
    {
      "title": "Data Connection Establishment",
      "description": "Connected data directly to Tableau, enabling auto-refresh to reports and reducing manual tasks",
      "order": 1,
      "is_completed": true,
      "completion_evidence": "Record 1: Connected data directly to Tableau"
    },
    {
      "title": "Automated Report Development",
      "description": "Developed automated weekly report through Tableau, eliminating manual tasks",
      "order": 2,
      "is_completed": true,
      "completion_evidence": "Record 2: Automated weekly report through Tableau"
    },
    {
      "title": "Reporting Sandbox Setup",
      "description": "Created Experian sandbox for risk trend identification and company-to-industry analyses",
      "order": 3,
      "is_completed": true,
      "completion_evidence": "Record 4: Created reporting Experian sandbox"
    },
    {
      "title": "Performance MIS Dashboard Development",
      "description": "Completed Phase One of the new Performance MIS dashboard in Tableau",
      "order": 4,
      "is_completed": true,
      "completion_evidence": "Record 5: Completed Phase One of Performance MIS dashboard"
    },
    {
      "title": "GitHub Repository Setup and Team Onboarding",
      "description": "Introduced GitHub Repository to the team, enabling versioning and collaboration",
      "order": 5,
      "is_completed": true,
      "completion_evidence": "Record 6: Introduced GitHub Repository to the team"
    },
    {
      "title": "Initial Team Training and Onboarding",
      "description": "Onboarded four team members to Tableau and advocated for automation",
      "order": 6,
      "is_completed": true,
      "completion_evidence": "Record 10: Onboarded four team members to Tableau"
    },
    {
      "title": "LDAP Group Setup for Tableau Access",
      "description": "Requested three LDAP groups for Tableau access, enabling organized report management",
      "order": 7,
      "is_completed": true,
      "completion_evidence": "Record 11: Requested three LDAP groups for Tableau access"
    }
  ],
  "next_suggested": "Develop additional dashboards and reports in Tableau, focusing on payment analysis and growth metrics",
  "confidence": 0.92
}
```

Note: The milestones are based on the actual progress records, and the next suggested step is a logical continuation of the work done so far, focusing on expanding the use of Tableau and developing more advanced analytics capabilities.
INFO 2025-06-05 22:25:32,213 autoreload 90273 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:25:32,929 autoreload 90621 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:25:39,959 autoreload 90621 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:25:40,722 autoreload 90699 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:25:42,917 basehttp 90699 6161739776 "GET /gobeyond/ HTTP/1.1" 200 282176
INFO 2025-06-05 22:25:42,983 basehttp 90699 6161739776 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 22:25:44,597 basehttp 90699 6161739776 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 943
ERROR 2025-06-05 22:25:50,783 views 90699 6161739776 Failed to parse AI milestone response: Here is the JSON object with the milestones and next suggested step:

```
{
  "milestones": [
    {
      "title": "Development of Financial Risk Management Framework",
      "description": "Created a new framework, called models, to enhance financial risk management and facilitate more effective insights.",
      "order": 1,
      "is_completed": true,
      "completion_evidence": "Record 1: Pioneered a new framework, called models, which will be available to everyone on the team."
    },
    {... Error: Expecting property name enclosed in double quotes: line 1 column 2 (char 1)
INFO 2025-06-05 22:25:50,793 basehttp 90699 6161739776 "POST /gobeyond/regenerate_milestones/3/ HTTP/1.1" 200 77
INFO 2025-06-05 22:25:50,802 basehttp 90699 6161739776 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 1021
INFO 2025-06-05 22:26:35,127 autoreload 90699 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:26:35,868 autoreload 91581 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:26:48,037 autoreload 91581 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/urls.py changed, reloading.
INFO 2025-06-05 22:26:48,646 autoreload 91638 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:26:51,106 basehttp 91638 6166687744 "GET /gobeyond/ HTTP/1.1" 200 282176
INFO 2025-06-05 22:26:51,144 basehttp 91638 6166687744 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-05 22:26:51,147 basehttp 91638 6166687744 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-05 22:26:51,155 basehttp 91638 6183514112 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-05 22:26:51,157 basehttp 91638 6166687744 "GET /static/js/milestone_tracker.js HTTP/1.1" 200 8721
INFO 2025-06-05 22:27:00,044 autoreload 91638 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/models.py changed, reloading.
INFO 2025-06-05 22:27:00,671 autoreload 91700 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:28:54,443 autoreload 91700 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:28:55,135 autoreload 92165 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:29:51,355 basehttp 92165 6128906240 "GET /gobeyond/ HTTP/1.1" 200 282176
INFO 2025-06-05 22:29:51,396 basehttp 92165 6145732608 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-05 22:29:51,397 basehttp 92165 6128906240 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-05 22:29:51,420 basehttp 92165 6128906240 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-05 22:29:51,424 basehttp 92165 6128906240 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 22:30:02,716 basehttp 92165 6128906240 "GET /gobeyond/ HTTP/1.1" 200 282176
INFO 2025-06-05 22:30:07,489 basehttp 92165 6128906240 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 936
INFO 2025-06-05 22:30:13,650 basehttp 92165 6128906240 "POST /gobeyond/regenerate_milestones/4/ HTTP/1.1" 200 77
INFO 2025-06-05 22:30:13,654 basehttp 92165 6128906240 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 3697
INFO 2025-06-05 22:34:13,626 basehttp 92165 6128906240 "POST /gobeyond/edit_goal/3/ HTTP/1.1" 302 0
INFO 2025-06-05 22:34:13,641 basehttp 92165 6128906240 "GET /gobeyond/ HTTP/1.1" 200 282085
INFO 2025-06-05 22:34:13,693 basehttp 92165 6128906240 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-05 22:34:13,771 basehttp 92165 6128906240 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-05 22:34:13,772 basehttp 92165 6145732608 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-05 22:34:13,776 basehttp 92165 6145732608 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 22:34:22,402 basehttp 92165 6162558976 "POST /gobeyond/edit_goal/2/ HTTP/1.1" 302 0
INFO 2025-06-05 22:34:22,422 basehttp 92165 6162558976 "GET /gobeyond/ HTTP/1.1" 200 281994
INFO 2025-06-05 22:34:30,996 basehttp 92165 6128906240 "POST /gobeyond/edit_goal/4/ HTTP/1.1" 302 0
INFO 2025-06-05 22:34:31,014 basehttp 92165 6128906240 "GET /gobeyond/ HTTP/1.1" 200 281903
INFO 2025-06-05 22:34:35,649 basehttp 92165 6128906240 "POST /gobeyond/edit_goal/3/ HTTP/1.1" 302 0
INFO 2025-06-05 22:34:35,671 basehttp 92165 6128906240 "GET /gobeyond/ HTTP/1.1" 200 281903
INFO 2025-06-05 22:34:38,598 basehttp 92165 6145732608 "POST /gobeyond/edit_goal/2/ HTTP/1.1" 302 0
INFO 2025-06-05 22:34:38,629 basehttp 92165 6145732608 "GET /gobeyond/ HTTP/1.1" 200 281903
INFO 2025-06-05 22:36:06,573 basehttp 92165 6128906240 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 1021
INFO 2025-06-05 22:36:19,819 autoreload 92165 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:36:20,646 autoreload 94085 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:36:37,233 autoreload 94085 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:36:37,889 autoreload 94152 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:36:54,233 autoreload 94152 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/models.py changed, reloading.
INFO 2025-06-05 22:36:54,843 autoreload 94219 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:37:23,590 autoreload 94219 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:37:24,221 autoreload 94369 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:37:51,022 autoreload 94369 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:37:51,635 autoreload 94479 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:38:04,890 autoreload 94479 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:38:05,539 autoreload 94548 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:39:10,392 basehttp 94548 6122418176 "GET /gobeyond/ HTTP/1.1" 200 281903
INFO 2025-06-05 22:39:10,425 basehttp 94548 6139244544 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-05 22:39:10,426 basehttp 94548 6122418176 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-05 22:39:10,446 basehttp 94548 6122418176 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-05 22:39:10,453 basehttp 94548 6122418176 "GET /static/js/milestone_tracker.js HTTP/1.1" 200 9307
INFO 2025-06-05 22:40:08,351 basehttp 94548 6122418176 "GET /gobeyond/ HTTP/1.1" 200 281903
INFO 2025-06-05 22:40:08,385 basehttp 94548 6122418176 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-05 22:40:08,387 basehttp 94548 6139244544 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-05 22:40:08,407 basehttp 94548 6139244544 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 22:40:12,166 basehttp 94548 6139244544 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 3977
INFO 2025-06-05 22:40:18,683 basehttp 94548 6122418176 "POST /gobeyond/regenerate_milestones/4/ HTTP/1.1" 200 77
INFO 2025-06-05 22:40:18,691 basehttp 94548 6122418176 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 3907
INFO 2025-06-05 22:41:56,743 basehttp 94548 6122418176 "GET /gobeyond/ HTTP/1.1" 200 281903
INFO 2025-06-05 22:41:56,807 basehttp 94548 6122418176 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 22:41:58,028 basehttp 94548 6122418176 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 3907
INFO 2025-06-05 22:41:59,384 basehttp 94548 6139244544 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 1126
ERROR 2025-06-05 22:42:07,044 views 94548 6139244544 Failed to parse AI milestone response: Here is the JSON object with the milestones and next suggested step:

```
{
  "milestones": [
    {
      "title": "Identifying High-Risk Customers",
      "description": "Discovered a way to identify customers with cross-product underperformance who puts our portfolio in risk",
      "order": 1,
      "is_completed": true,
      "completion_evidence": "Record 1: 2024-06-13",
      "estimated_completion_date": null
    },
    {
      "title": "Assisting in Code Development",
      "description":... Error: Expecting property name enclosed in double quotes: line 1 column 2 (char 1)
INFO 2025-06-05 22:42:07,059 basehttp 94548 6139244544 "POST /gobeyond/regenerate_milestones/3/ HTTP/1.1" 200 77
INFO 2025-06-05 22:42:07,069 basehttp 94548 6139244544 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 1126
INFO 2025-06-05 22:43:50,806 autoreload 94548 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:43:51,714 autoreload 95968 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:44:12,574 autoreload 95968 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:44:13,268 autoreload 96064 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:46:26,194 autoreload 96064 8619180608 /Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/openai/types/beta/threads/text.py changed, reloading.
INFO 2025-06-05 22:46:26,893 autoreload 96717 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:46:37,600 autoreload 96717 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:46:38,258 autoreload 96782 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:46:44,871 autoreload 96782 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:46:45,535 autoreload 96823 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:46:51,634 autoreload 96823 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:46:52,302 autoreload 96852 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:47:00,361 autoreload 96852 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:47:00,987 autoreload 96901 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:47:18,457 autoreload 96901 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:47:19,096 autoreload 96975 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:50:27,311 basehttp 96975 6122942464 "GET /gobeyond/ HTTP/1.1" 200 281903
INFO 2025-06-05 22:50:27,353 basehttp 96975 6122942464 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-05 22:50:27,356 basehttp 96975 6122942464 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-05 22:50:27,364 basehttp 96975 6139768832 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-05 22:50:27,364 basehttp 96975 6122942464 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-05 22:50:27,404 basehttp 96975 6122942464 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-05 22:50:27,405 basehttp 96975 6139768832 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 22:50:29,709 basehttp 96975 6122942464 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 3907
INFO 2025-06-05 22:51:05,506 basehttp 96975 6122942464 "POST /gobeyond/regenerate_milestones/4/ HTTP/1.1" 200 77
INFO 2025-06-05 22:51:05,515 basehttp 96975 6122942464 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4399
INFO 2025-06-05 22:52:55,249 basehttp 96975 6122942464 "GET /gobeyond/ HTTP/1.1" 200 281903
INFO 2025-06-05 22:52:55,296 basehttp 96975 6122942464 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 22:52:56,596 basehttp 96975 6122942464 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 1126
INFO 2025-06-05 22:52:59,865 basehttp 96975 6122942464 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4399
INFO 2025-06-05 22:53:40,549 basehttp 96975 6139768832 "GET /gobeyond/ HTTP/1.1" 200 281903
INFO 2025-06-05 22:53:45,347 basehttp 96975 6122942464 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4399
INFO 2025-06-05 22:56:01,558 basehttp 96975 6139768832 "POST /gobeyond/edit_record/4/48/ HTTP/1.1" 302 0
INFO 2025-06-05 22:56:01,588 basehttp 96975 6139768832 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 22:56:01,633 basehttp 96975 6139768832 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-05 22:56:01,633 basehttp 96975 6156595200 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 22:56:41,256 basehttp 96975 6122942464 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4498
INFO 2025-06-05 22:56:49,610 basehttp 96975 6139768832 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 1126
INFO 2025-06-05 22:57:03,406 autoreload 96975 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/pisakhov/__init__.py changed, reloading.
INFO 2025-06-05 22:57:04,563 autoreload 99511 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:57:29,712 autoreload 99511 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/pisakhov/settings.py changed, reloading.
INFO 2025-06-05 22:57:30,469 autoreload 99622 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:57:59,580 autoreload 99622 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/models.py changed, reloading.
INFO 2025-06-05 22:58:00,251 autoreload 99739 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:59:13,228 autoreload 99739 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:59:13,967 autoreload 248 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:59:17,514 basehttp 248 6165049344 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4498
INFO 2025-06-05 22:59:18,187 basehttp 248 6165049344 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4498
INFO 2025-06-05 22:59:18,939 basehttp 248 6165049344 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4498
INFO 2025-06-05 22:59:41,116 autoreload 248 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 22:59:41,850 autoreload 405 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 22:59:52,305 autoreload 405 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/urls.py changed, reloading.
INFO 2025-06-05 22:59:53,319 autoreload 473 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:01:50,773 autoreload 473 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:01:51,569 autoreload 1004 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:02:00,923 autoreload 1004 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:02:01,685 autoreload 1060 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:02:07,306 basehttp 1060 6202617856 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:02:07,331 basehttp 1060 6202617856 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-05 23:02:07,341 basehttp 1060 6202617856 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-05 23:02:07,341 basehttp 1060 6219444224 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-05 23:02:07,399 basehttp 1060 6236270592 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-05 23:02:07,399 basehttp 1060 6202617856 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-05 23:02:07,418 basehttp 1060 6202617856 "GET /static/js/milestone_tracker.js HTTP/1.1" 200 13409
INFO 2025-06-05 23:02:09,070 basehttp 1060 6202617856 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4498
INFO 2025-06-05 23:02:12,302 autoreload 1060 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:02:13,175 autoreload 1138 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:03:08,279 basehttp 1138 6131904512 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:03:08,317 basehttp 1138 6131904512 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:03:12,902 basehttp 1138 6131904512 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4498
INFO 2025-06-05 23:03:16,169 basehttp 1138 6148730880 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:03:17,574 basehttp 1138 6165557248 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
INFO 2025-06-05 23:03:20,914 basehttp 1138 6165557248 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:03:20,931 basehttp 1138 6165557248 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:03:22,175 basehttp 1138 6165557248 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
ERROR 2025-06-05 23:03:33,139 views 1138 6131904512 Error starting milestone regeneration: Error 61 connecting to localhost:6379. Connection refused.
ERROR 2025-06-05 23:03:33,143 log 1138 6131904512 Internal Server Error: /gobeyond/regenerate_milestones/4/
INFO 2025-06-05 23:03:33,146 basehttp 1138 6131904512 - Broken pipe from ('127.0.0.1', 54775)
ERROR 2025-06-05 23:03:44,076 views 1138 6148730880 Error starting milestone regeneration: Error 61 connecting to localhost:6379. Connection refused.
ERROR 2025-06-05 23:03:44,078 log 1138 6148730880 Internal Server Error: /gobeyond/regenerate_milestones/4/
ERROR 2025-06-05 23:03:44,079 basehttp 1138 6148730880 "POST /gobeyond/regenerate_milestones/4/ HTTP/1.1" 500 51
WARNING 2025-06-05 23:03:56,441 log 1138 6165557248 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-05 23:03:56,441 basehttp 1138 6165557248 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-05 23:03:59,598 basehttp 1138 12901707776 "GET /gobeyond/ HTTP/1.1" 200 281901
WARNING 2025-06-05 23:03:59,631 log 1138 12901707776 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-05 23:03:59,632 basehttp 1138 12901707776 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-05 23:03:59,651 basehttp 1138 12901707776 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:04:02,052 basehttp 1138 12901707776 "GET /gobeyond/ HTTP/1.1" 200 281901
WARNING 2025-06-05 23:04:02,085 log 1138 12901707776 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-05 23:04:02,086 basehttp 1138 12901707776 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-05 23:04:02,101 basehttp 1138 12918534144 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-05 23:04:02,105 basehttp 1138 12918534144 "GET /static/js/utils.js HTTP/1.1" 200 5217
INFO 2025-06-05 23:04:02,145 basehttp 1138 12918534144 "GET /static/js/dialog_manager.js HTTP/1.1" 200 2606
INFO 2025-06-05 23:04:02,147 basehttp 1138 12918534144 "GET /static/js/add_record.js HTTP/1.1" 200 3342
INFO 2025-06-05 23:04:02,174 basehttp 1138 12918534144 "GET /static/js/media_handling.js HTTP/1.1" 200 7603
INFO 2025-06-05 23:04:02,174 basehttp 1138 12901707776 "GET /static/js/goal_management.js HTTP/1.1" 200 3089
INFO 2025-06-05 23:04:02,176 basehttp 1138 12901707776 "GET /static/js/milestone_tracker.js HTTP/1.1" 200 13409
INFO 2025-06-05 23:04:06,252 basehttp 1138 6165557248 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
ERROR 2025-06-05 23:04:26,066 views 1138 6165557248 Error starting milestone regeneration: Error 61 connecting to localhost:6379. Connection refused.
ERROR 2025-06-05 23:04:26,070 log 1138 6165557248 Internal Server Error: /gobeyond/regenerate_milestones/4/
ERROR 2025-06-05 23:04:26,071 basehttp 1138 6165557248 "POST /gobeyond/regenerate_milestones/4/ HTTP/1.1" 500 51
INFO 2025-06-05 23:04:30,882 autoreload 1138 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:04:31,841 autoreload 1738 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:04:42,439 autoreload 1738 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:04:43,131 autoreload 1793 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:04:54,582 autoreload 1793 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:04:55,268 autoreload 1851 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:05:05,544 autoreload 1851 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:05:06,228 autoreload 1903 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:05:19,623 autoreload 1903 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/pisakhov/__init__.py changed, reloading.
INFO 2025-06-05 23:05:20,317 autoreload 1972 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:05:29,969 basehttp 1972 6128693248 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
INFO 2025-06-05 23:05:30,662 autoreload 1972 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/pisakhov/settings.py changed, reloading.
INFO 2025-06-05 23:05:31,282 autoreload 2025 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:07:32,551 basehttp 2025 6129446912 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:07:32,641 basehttp 2025 6129446912 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:07:34,098 basehttp 2025 6129446912 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
INFO 2025-06-05 23:07:35,813 basehttp 2025 6129446912 "POST /gobeyond/regenerate_milestones/4/ HTTP/1.1" 200 134
INFO 2025-06-05 23:07:37,828 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:07:42,768 basehttp 2025 6146273280 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:07:44,056 basehttp 2025 6146273280 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
INFO 2025-06-05 23:07:50,220 basehttp 2025 6129446912 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:07:51,297 basehttp 2025 6129446912 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
INFO 2025-06-05 23:07:53,873 basehttp 2025 6146273280 "POST /gobeyond/regenerate_milestones/4/ HTTP/1.1" 200 149
INFO 2025-06-05 23:07:55,888 basehttp 2025 6146273280 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:08:00,901 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:08:05,921 basehttp 2025 6146273280 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:08:10,937 basehttp 2025 6179926016 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:08:15,956 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:08:15,966 basehttp 2025 6129446912 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4526
INFO 2025-06-05 23:08:33,509 basehttp 2025 6146273280 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:08:33,529 basehttp 2025 6146273280 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:08:35,025 basehttp 2025 6146273280 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4526
INFO 2025-06-05 23:08:36,385 basehttp 2025 6146273280 "POST /gobeyond/regenerate_milestones/4/ HTTP/1.1" 200 134
INFO 2025-06-05 23:08:38,406 basehttp 2025 6146273280 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:08:40,484 basehttp 2025 6146273280 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:08:41,631 basehttp 2025 6146273280 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
INFO 2025-06-05 23:09:01,435 basehttp 2025 6146273280 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:09:02,378 basehttp 2025 6146273280 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
INFO 2025-06-05 23:09:12,755 basehttp 2025 6163099648 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:09:23,973 basehttp 2025 6129446912 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:09:23,999 basehttp 2025 6129446912 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:09:25,145 basehttp 2025 6129446912 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4240
INFO 2025-06-05 23:12:42,045 basehttp 2025 6146273280 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:12:42,074 basehttp 2025 6163099648 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-05 23:12:42,074 basehttp 2025 6146273280 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-05 23:12:42,076 basehttp 2025 6146273280 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-05 23:12:42,099 basehttp 2025 6146273280 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-05 23:12:42,100 basehttp 2025 6163099648 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-05 23:12:42,110 basehttp 2025 6163099648 "GET /static/js/milestone_tracker.js HTTP/1.1" 200 15814
INFO 2025-06-05 23:12:43,327 basehttp 2025 6129446912 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4240
INFO 2025-06-05 23:12:43,334 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:12:44,055 basehttp 2025 6129446912 "POST /gobeyond/regenerate_milestones/4/ HTTP/1.1" 200 134
INFO 2025-06-05 23:12:46,057 basehttp 2025 6129446912 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:12:46,070 basehttp 2025 6129446912 - Broken pipe from ('127.0.0.1', 55407)
INFO 2025-06-05 23:12:47,268 basehttp 2025 6163099648 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
INFO 2025-06-05 23:12:47,276 basehttp 2025 6163099648 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:12:49,304 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:12:54,315 basehttp 2025 6163099648 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:12:56,788 basehttp 2025 6163099648 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:12:56,805 basehttp 2025 6163099648 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:12:57,895 basehttp 2025 6129446912 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
INFO 2025-06-05 23:12:57,902 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:12:59,927 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:13:04,953 basehttp 2025 6163099648 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:13:09,962 basehttp 2025 6179926016 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:13:14,993 basehttp 2025 6196752384 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:13:17,715 basehttp 2025 6129446912 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:13:17,741 basehttp 2025 6129446912 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:13:19,046 basehttp 2025 6129446912 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4424
INFO 2025-06-05 23:13:19,054 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:13:21,393 basehttp 2025 6146273280 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 1126
INFO 2025-06-05 23:13:21,398 basehttp 2025 6146273280 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 90
INFO 2025-06-05 23:13:22,415 basehttp 2025 6146273280 "POST /gobeyond/regenerate_milestones/3/ HTTP/1.1" 200 134
INFO 2025-06-05 23:13:24,431 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:13:29,449 basehttp 2025 6146273280 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:13:34,466 basehttp 2025 6179926016 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:13:35,991 basehttp 2025 6179926016 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:13:36,007 basehttp 2025 6179926016 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:13:38,159 basehttp 2025 6129446912 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 120
INFO 2025-06-05 23:13:38,164 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:13:40,192 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:13:45,210 basehttp 2025 6146273280 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:13:50,228 basehttp 2025 6179926016 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:13:55,243 basehttp 2025 6196752384 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:14:00,263 basehttp 2025 6213578752 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:14:05,285 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:14:10,304 basehttp 2025 6146273280 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:14:10,321 basehttp 2025 6146273280 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 6021
INFO 2025-06-05 23:14:10,338 basehttp 2025 6146273280 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:14:27,788 basehttp 2025 6163099648 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 1164
INFO 2025-06-05 23:14:27,796 basehttp 2025 6163099648 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 90
INFO 2025-06-05 23:14:31,221 basehttp 2025 6163099648 "POST /gobeyond/regenerate_milestones/2/ HTTP/1.1" 200 134
INFO 2025-06-05 23:14:33,237 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:14:38,261 basehttp 2025 6146273280 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:14:43,283 basehttp 2025 6163099648 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:14:48,296 basehttp 2025 6196752384 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:14:53,317 basehttp 2025 6213578752 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:14:58,329 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:15:03,343 basehttp 2025 6146273280 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:15:08,369 basehttp 2025 6163099648 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:15:11,731 basehttp 2025 6163099648 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:15:11,774 basehttp 2025 6163099648 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:15:13,174 basehttp 2025 6129446912 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 5091
INFO 2025-06-05 23:15:13,179 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:15:51,873 basehttp 2025 6146273280 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4424
INFO 2025-06-05 23:15:51,880 basehttp 2025 6146273280 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:15:53,584 basehttp 2025 6163099648 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4424
INFO 2025-06-05 23:15:53,594 basehttp 2025 6163099648 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:15:56,393 basehttp 2025 6163099648 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4424
INFO 2025-06-05 23:15:56,401 basehttp 2025 6163099648 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:15:59,294 basehttp 2025 6179926016 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4424
INFO 2025-06-05 23:15:59,298 basehttp 2025 6179926016 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:16:22,939 basehttp 2025 6129446912 "GET /gobeyond/ HTTP/1.1" 200 281901
INFO 2025-06-05 23:16:23,006 basehttp 2025 6129446912 "GET /static/js/milestone_tracker.js HTTP/1.1" 200 14500
ERROR 2025-06-05 23:16:28,155 log 2025 6146273280 Internal Server Error: /gobeyond/edit_record/4/48/
Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 354, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.IntegrityError: UNIQUE constraint failed: gobeyond_milestonetask.goal_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/contrib/auth/decorators.py", line 60, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py", line 702, in edit_record
    milestone_task = MilestoneTask.objects.create(
        goal=record.goal,
        task_id=task_id,
        status='PENDING'
    )
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/query.py", line 679, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/base.py", line 892, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/base.py", line 998, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/base.py", line 1161, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/base.py", line 1202, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/query.py", line 1847, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1836, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 354, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: UNIQUE constraint failed: gobeyond_milestonetask.goal_id
ERROR 2025-06-05 23:16:28,159 basehttp 2025 6146273280 "POST /gobeyond/edit_record/4/48/ HTTP/1.1" 500 176396
ERROR 2025-06-05 23:16:32,382 log 2025 6146273280 Internal Server Error: /gobeyond/edit_record/4/48/
Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 354, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.IntegrityError: UNIQUE constraint failed: gobeyond_milestonetask.goal_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/contrib/auth/decorators.py", line 60, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py", line 702, in edit_record
    milestone_task = MilestoneTask.objects.create(
        goal=record.goal,
        task_id=task_id,
        status='PENDING'
    )
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/query.py", line 679, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/base.py", line 892, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/base.py", line 998, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/base.py", line 1161, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/base.py", line 1202, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/query.py", line 1847, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1836, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 354, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: UNIQUE constraint failed: gobeyond_milestonetask.goal_id
ERROR 2025-06-05 23:16:32,384 basehttp 2025 6146273280 "POST /gobeyond/edit_record/4/48/ HTTP/1.1" 500 176396
INFO 2025-06-05 23:16:42,142 basehttp 2025 6129446912 "GET /gobeyond/ HTTP/1.1" 200 281903
INFO 2025-06-05 23:16:42,198 basehttp 2025 6129446912 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:16:43,630 basehttp 2025 6163099648 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
INFO 2025-06-05 23:16:43,636 basehttp 2025 6163099648 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:17:02,362 basehttp 2025 6179926016 "GET /gobeyond/ HTTP/1.1" 200 281903
INFO 2025-06-05 23:17:02,379 basehttp 2025 6179926016 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:17:03,783 basehttp 2025 6129446912 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
INFO 2025-06-05 23:17:03,791 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:17:04,955 basehttp 2025 6129446912 "POST /gobeyond/regenerate_milestones/4/ HTTP/1.1" 200 134
INFO 2025-06-05 23:17:06,253 basehttp 2025 6129446912 "GET /gobeyond/ HTTP/1.1" 200 281903
INFO 2025-06-05 23:17:07,315 basehttp 2025 6129446912 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
INFO 2025-06-05 23:17:07,322 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:17:09,351 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:17:11,190 basehttp 2025 6129446912 "GET /gobeyond/ HTTP/1.1" 200 281903
INFO 2025-06-05 23:17:11,208 basehttp 2025 6129446912 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:17:12,631 basehttp 2025 6179926016 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
INFO 2025-06-05 23:17:12,637 basehttp 2025 6179926016 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:17:14,661 basehttp 2025 6179926016 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:17:19,689 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:17:24,706 basehttp 2025 6179926016 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:17:29,720 basehttp 2025 6196752384 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:17:34,735 basehttp 2025 6213578752 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:17:39,755 basehttp 2025 6230405120 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:17:44,781 basehttp 2025 6129446912 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:17:49,792 basehttp 2025 6163099648 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:17:49,799 basehttp 2025 6163099648 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4264
INFO 2025-06-05 23:17:49,806 basehttp 2025 6163099648 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:17:56,333 basehttp 2025 6179926016 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4264
INFO 2025-06-05 23:17:56,341 basehttp 2025 6179926016 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
ERROR 2025-06-05 23:18:16,771 log 2025 6196752384 Internal Server Error: /gobeyond/edit_record/4/48/
Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 354, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.IntegrityError: UNIQUE constraint failed: gobeyond_milestonetask.goal_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/contrib/auth/decorators.py", line 60, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py", line 702, in edit_record
    milestone_task = MilestoneTask.objects.create(
        goal=record.goal,
        task_id=task_id,
        status='PENDING'
    )
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/query.py", line 679, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/base.py", line 892, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/base.py", line 998, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/base.py", line 1161, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/base.py", line 1202, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/query.py", line 1847, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/models/sql/compiler.py", line 1836, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/db/backends/sqlite3/base.py", line 354, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: UNIQUE constraint failed: gobeyond_milestonetask.goal_id
ERROR 2025-06-05 23:18:16,775 basehttp 2025 6196752384 "POST /gobeyond/edit_record/4/48/ HTTP/1.1" 500 176397
INFO 2025-06-05 23:19:14,342 autoreload 2025 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:19:15,186 autoreload 5839 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:19:22,465 autoreload 5839 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:19:23,204 autoreload 5888 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:19:29,266 autoreload 5888 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:19:30,042 autoreload 5922 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:19:37,168 autoreload 5922 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:19:37,799 autoreload 5977 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:25:04,531 basehttp 5977 6134001664 "GET /gobeyond/ HTTP/1.1" 200 281905
INFO 2025-06-05 23:25:04,583 basehttp 5977 6150828032 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-05 23:25:04,584 basehttp 5977 6134001664 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-05 23:25:04,608 basehttp 5977 6134001664 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-05 23:25:04,609 basehttp 5977 6150828032 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-05 23:25:04,620 basehttp 5977 6134001664 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:25:04,621 basehttp 5977 6150828032 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-05 23:25:10,225 basehttp 5977 6134001664 "POST /gobeyond/edit_record/4/48/ HTTP/1.1" 302 0
INFO 2025-06-05 23:25:10,249 basehttp 5977 6134001664 "GET /gobeyond/ HTTP/1.1" 200 281903
INFO 2025-06-05 23:25:11,585 basehttp 5977 6134001664 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
INFO 2025-06-05 23:25:11,595 basehttp 5977 6134001664 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:13,350 basehttp 5977 6134001664 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 6021
INFO 2025-06-05 23:25:13,359 basehttp 5977 6134001664 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:13,620 basehttp 5977 6134001664 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:18,639 basehttp 5977 6150828032 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:23,654 basehttp 5977 6325039104 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:27,577 basehttp 5977 6325039104 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 6021
INFO 2025-06-05 23:25:27,583 basehttp 5977 6325039104 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:28,591 basehttp 5977 6134001664 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
INFO 2025-06-05 23:25:28,596 basehttp 5977 6134001664 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:28,663 basehttp 5977 6134001664 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:29,096 basehttp 5977 6134001664 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 113
INFO 2025-06-05 23:25:29,104 basehttp 5977 6134001664 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:30,605 basehttp 5977 6134001664 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:30,609 basehttp 5977 6150828032 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 5091
INFO 2025-06-05 23:25:30,614 basehttp 5977 6150828032 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:31,119 basehttp 5977 6150828032 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:33,679 basehttp 5977 6325039104 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:35,614 basehttp 5977 6325039104 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:36,150 basehttp 5977 6325039104 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:38,694 basehttp 5977 6134001664 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:40,623 basehttp 5977 6134001664 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:41,159 basehttp 5977 6134001664 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:43,705 basehttp 5977 6134001664 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:43,710 basehttp 5977 6134001664 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4409
INFO 2025-06-05 23:25:43,715 basehttp 5977 6134001664 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:44,287 autoreload 5977 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:25:45,101 autoreload 7613 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:25:50,641 basehttp 7613 6138589184 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:50,650 basehttp 7613 6138589184 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4409
INFO 2025-06-05 23:25:50,660 basehttp 7613 6138589184 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:51,172 basehttp 7613 6138589184 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:25:51,177 basehttp 7613 6138589184 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4409
INFO 2025-06-05 23:25:51,183 basehttp 7613 6138589184 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:26:30,991 basehttp 7613 6138015744 "GET /gobeyond/ HTTP/1.1" 200 281903
INFO 2025-06-05 23:26:31,048 basehttp 7613 6138015744 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:26:32,192 basehttp 7613 6138015744 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4409
INFO 2025-06-05 23:26:32,200 basehttp 7613 6138015744 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:26:37,182 basehttp 7613 6138015744 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4409
INFO 2025-06-05 23:26:37,193 basehttp 7613 6138015744 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:26:53,795 basehttp 7613 6154842112 "GET /gobeyond/ HTTP/1.1" 200 281903
INFO 2025-06-05 23:27:25,425 basehttp 7613 6138015744 "POST /gobeyond/edit_goal/4/ HTTP/1.1" 302 0
INFO 2025-06-05 23:27:25,440 basehttp 7613 6138015744 "GET /gobeyond/ HTTP/1.1" 200 281994
INFO 2025-06-05 23:27:27,100 basehttp 7613 6138015744 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4409
INFO 2025-06-05 23:27:27,109 basehttp 7613 6138015744 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:27:28,990 basehttp 7613 6154842112 "POST /gobeyond/regenerate_milestones/4/ HTTP/1.1" 200 134
INFO 2025-06-05 23:27:31,004 basehttp 7613 6154842112 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:27:36,022 basehttp 7613 6138015744 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:27:41,032 basehttp 7613 6154842112 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:27:46,045 basehttp 7613 6188494848 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:27:51,062 basehttp 7613 6205321216 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:27:56,071 basehttp 7613 6138015744 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:28:01,083 basehttp 7613 6154842112 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:28:06,093 basehttp 7613 6188494848 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:28:11,100 basehttp 7613 6138015744 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:28:11,105 basehttp 7613 6138015744 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 5122
INFO 2025-06-05 23:28:11,110 basehttp 7613 6138015744 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:28:22,648 basehttp 7613 6154842112 "GET /gobeyond/ HTTP/1.1" 200 281994
INFO 2025-06-05 23:28:22,706 basehttp 7613 6154842112 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:28:24,360 basehttp 7613 6138015744 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 5122
INFO 2025-06-05 23:28:24,368 basehttp 7613 6138015744 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:32:49,198 autoreload 7613 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:32:50,096 autoreload 9404 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:33:00,603 autoreload 9404 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:33:01,249 autoreload 9467 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:33:14,655 autoreload 9467 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:33:15,339 autoreload 9529 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:33:29,830 autoreload 9529 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:33:30,502 autoreload 9594 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:33:48,201 autoreload 9594 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:33:48,889 autoreload 9675 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:34:01,280 autoreload 9675 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:34:01,948 autoreload 9734 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:34:15,850 basehttp 9734 6162493440 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 5193
INFO 2025-06-05 23:34:15,857 basehttp 9734 6162493440 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:34:20,725 basehttp 9734 6162493440 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 5162
INFO 2025-06-05 23:34:20,741 basehttp 9734 6162493440 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:35:21,385 autoreload 9734 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:35:22,174 autoreload 10100 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:35:31,488 autoreload 10100 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:35:32,147 autoreload 10143 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:35:43,390 autoreload 10143 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:35:43,990 autoreload 10197 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:36:01,637 autoreload 10197 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:36:02,395 autoreload 10282 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:36:27,165 basehttp 10282 6172553216 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 5193
INFO 2025-06-05 23:36:27,174 basehttp 10282 6172553216 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:36:27,885 basehttp 10282 6172553216 "POST /gobeyond/regenerate_milestones/4/ HTTP/1.1" 200 134
INFO 2025-06-05 23:36:29,893 basehttp 10282 6206205952 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:36:29,902 basehttp 10282 6172553216 "GET /gobeyond/ HTTP/1.1" 200 281994
INFO 2025-06-05 23:36:29,957 basehttp 10282 6172553216 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-05 23:36:29,961 basehttp 10282 6206205952 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-05 23:36:29,984 basehttp 10282 6206205952 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-05 23:36:29,986 basehttp 10282 6172553216 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-05 23:36:29,989 basehttp 10282 6172553216 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-05 23:36:29,993 basehttp 10282 6206205952 "GET /static/js/milestone_tracker.js HTTP/1.1" 200 17517
INFO 2025-06-05 23:36:31,091 basehttp 10282 6206205952 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 184
INFO 2025-06-05 23:36:31,095 basehttp 10282 6206205952 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:36:33,116 basehttp 10282 6172553216 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:36:38,142 basehttp 10282 6206205952 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:36:43,156 basehttp 10282 6223032320 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:36:44,473 basehttp 10282 6223032320 "GET /gobeyond/ HTTP/1.1" 200 281994
INFO 2025-06-05 23:36:44,531 basehttp 10282 6223032320 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:36:45,896 basehttp 10282 6223032320 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 184
INFO 2025-06-05 23:36:45,902 basehttp 10282 6223032320 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:36:47,924 basehttp 10282 6172553216 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:36:52,942 basehttp 10282 6206205952 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:36:57,955 basehttp 10282 6223032320 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:37:02,978 basehttp 10282 6239858688 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:37:04,539 basehttp 10282 6239858688 "GET /gobeyond/ HTTP/1.1" 200 281994
INFO 2025-06-05 23:37:04,560 basehttp 10282 6239858688 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:37:05,909 basehttp 10282 6239858688 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 184
INFO 2025-06-05 23:37:05,926 basehttp 10282 6239858688 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:37:07,945 basehttp 10282 6172553216 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:37:07,949 basehttp 10282 6172553216 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4969
INFO 2025-06-05 23:37:07,953 basehttp 10282 6172553216 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:38:33,535 basehttp 10282 6189379584 "GET /gobeyond/ HTTP/1.1" 200 281994
INFO 2025-06-05 23:38:33,591 basehttp 10282 6189379584 "GET /static/js/milestone_tracker.js HTTP/1.1" 200 17536
INFO 2025-06-05 23:38:34,605 basehttp 10282 6189379584 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4969
INFO 2025-06-05 23:38:34,625 basehttp 10282 6189379584 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:38:58,970 basehttp 10282 6172553216 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 6092
INFO 2025-06-05 23:38:58,979 basehttp 10282 6172553216 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:39:04,922 basehttp 10282 6189379584 "POST /gobeyond/regenerate_milestones/3/ HTTP/1.1" 200 134
INFO 2025-06-05 23:39:06,941 basehttp 10282 6189379584 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:39:11,958 basehttp 10282 6223032320 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:39:12,336 basehttp 10282 6223032320 "GET /gobeyond/ HTTP/1.1" 200 281994
INFO 2025-06-05 23:39:12,387 basehttp 10282 6223032320 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:39:13,497 basehttp 10282 6172553216 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 191
INFO 2025-06-05 23:39:13,505 basehttp 10282 6172553216 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:39:15,533 basehttp 10282 6172553216 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:39:20,551 basehttp 10282 6189379584 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:39:25,565 basehttp 10282 6172553216 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:39:30,582 basehttp 10282 6189379584 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:39:35,594 basehttp 10282 6223032320 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:39:40,613 basehttp 10282 6239858688 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:39:45,624 basehttp 10282 6206205952 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:39:45,631 basehttp 10282 6206205952 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 5265
INFO 2025-06-05 23:39:45,637 basehttp 10282 6206205952 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:45:17,458 basehttp 10282 6172553216 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 5162
INFO 2025-06-05 23:45:17,467 basehttp 10282 6172553216 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:45:22,994 autoreload 10282 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:45:23,830 autoreload 12541 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:45:42,794 autoreload 12541 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:45:43,490 autoreload 12624 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:45:48,553 basehttp 12624 6197129216 "GET /gobeyond/ HTTP/1.1" 200 281994
INFO 2025-06-05 23:45:48,638 basehttp 12624 6197129216 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:45:49,739 basehttp 12624 6197129216 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4969
INFO 2025-06-05 23:45:49,743 basehttp 12624 6197129216 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:45:55,938 autoreload 12624 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:45:56,593 autoreload 12697 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:46:13,254 autoreload 12697 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:46:13,934 autoreload 12765 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:46:34,405 basehttp 12765 6199848960 "POST /gobeyond/archive_goal/4/ HTTP/1.1" 302 0
INFO 2025-06-05 23:46:34,425 basehttp 12765 6199848960 "GET /gobeyond/ HTTP/1.1" 200 206159
INFO 2025-06-05 23:46:34,483 basehttp 12765 6199848960 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-05 23:46:34,488 basehttp 12765 6199848960 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:46:35,907 basehttp 12765 6199848960 "GET /gobeyond/?archived=true HTTP/1.1" 200 89550
INFO 2025-06-05 23:46:37,182 basehttp 12765 6199848960 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4969
INFO 2025-06-05 23:46:37,194 basehttp 12765 6199848960 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-05 23:46:41,280 basehttp 12765 6199848960 "GET /gobeyond/ HTTP/1.1" 200 206159
INFO 2025-06-05 23:47:25,140 autoreload 12765 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:47:25,879 autoreload 13042 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:48:13,762 autoreload 13042 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:48:14,635 autoreload 13307 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:49:51,767 autoreload 13307 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:49:52,634 autoreload 13721 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:50:06,133 autoreload 13721 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:50:06,751 autoreload 13790 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:51:36,275 basehttp 13790 6124613632 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 5265
INFO 2025-06-05 23:51:36,283 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:51:37,172 basehttp 13790 6124613632 "POST /gobeyond/regenerate_milestones/3/ HTTP/1.1" 200 134
INFO 2025-06-05 23:51:39,200 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:51:40,858 basehttp 13790 6124613632 "GET /gobeyond/ HTTP/1.1" 200 206159
INFO 2025-06-05 23:51:40,889 basehttp 13790 6124613632 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-05 23:51:40,895 basehttp 13790 6124613632 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-05 23:51:40,898 basehttp 13790 6158266368 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-05 23:51:40,902 basehttp 13790 6158266368 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-05 23:51:40,926 basehttp 13790 6158266368 "GET /static/js/milestone_tracker.js HTTP/1.1" 200 17945
INFO 2025-06-05 23:51:42,130 basehttp 13790 6158266368 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 191
INFO 2025-06-05 23:51:42,139 basehttp 13790 6158266368 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:51:44,174 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:51:49,193 basehttp 13790 6175092736 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:51:54,206 basehttp 13790 6191919104 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:51:59,225 basehttp 13790 6208745472 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:52:04,242 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:52:09,262 basehttp 13790 6158266368 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:52:14,271 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:52:14,276 basehttp 13790 6124613632 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 5015
INFO 2025-06-05 23:52:14,283 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:52:26,270 basehttp 13790 6141440000 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 5162
INFO 2025-06-05 23:52:26,280 basehttp 13790 6141440000 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:52:34,748 basehttp 13790 6124613632 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 5162
INFO 2025-06-05 23:52:34,761 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:52:36,011 basehttp 13790 6124613632 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 5162
INFO 2025-06-05 23:52:36,019 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:00,820 basehttp 13790 6141440000 "POST /gobeyond/regenerate_milestones/2/ HTTP/1.1" 200 134
INFO 2025-06-05 23:53:02,001 basehttp 13790 6141440000 "GET /gobeyond/ HTTP/1.1" 200 206159
INFO 2025-06-05 23:53:02,020 basehttp 13790 6141440000 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-05 23:53:03,197 basehttp 13790 6124613632 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 5015
INFO 2025-06-05 23:53:03,204 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:06,604 basehttp 13790 6124613632 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 186
INFO 2025-06-05 23:53:06,609 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:08,162 basehttp 13790 6141440000 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 5015
INFO 2025-06-05 23:53:08,174 basehttp 13790 6141440000 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:08,640 basehttp 13790 6141440000 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:09,318 basehttp 13790 6141440000 "GET /gobeyond/ HTTP/1.1" 200 206159
INFO 2025-06-05 23:53:10,788 basehttp 13790 6141440000 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 5015
INFO 2025-06-05 23:53:10,794 basehttp 13790 6141440000 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:12,680 basehttp 13790 6141440000 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 186
INFO 2025-06-05 23:53:12,694 basehttp 13790 6141440000 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:14,717 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:19,743 basehttp 13790 6141440000 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:24,763 basehttp 13790 6175092736 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:25,753 basehttp 13790 6175092736 "POST /gobeyond/regenerate_milestones/3/ HTTP/1.1" 200 134
INFO 2025-06-05 23:53:26,752 basehttp 13790 6175092736 "GET /gobeyond/ HTTP/1.1" 200 206159
INFO 2025-06-05 23:53:28,340 basehttp 13790 6124613632 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 186
INFO 2025-06-05 23:53:28,358 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:29,806 basehttp 13790 6124613632 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 191
INFO 2025-06-05 23:53:29,824 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:30,381 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:31,832 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:35,416 basehttp 13790 6141440000 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:36,847 basehttp 13790 6141440000 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:40,432 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:40,444 basehttp 13790 6124613632 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 6182
INFO 2025-06-05 23:53:40,454 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:41,865 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:46,877 basehttp 13790 6141440000 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:51,903 basehttp 13790 6158266368 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:53:56,911 basehttp 13790 6175092736 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:54:01,920 basehttp 13790 6124613632 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:54:06,933 basehttp 13790 6141440000 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:54:11,941 basehttp 13790 6158266368 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:54:11,947 basehttp 13790 6158266368 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 5625
INFO 2025-06-05 23:54:11,962 basehttp 13790 6158266368 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-05 23:59:18,481 autoreload 13790 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:59:19,408 autoreload 15978 8619180608 Watching for file changes with StatReloader
INFO 2025-06-05 23:59:36,253 autoreload 15978 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-05 23:59:37,053 autoreload 16076 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:00:40,509 autoreload 16076 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-06 00:00:41,384 autoreload 16452 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:03:00,713 basehttp 16452 6164819968 "GET /gobeyond/ HTTP/1.1" 200 206159
INFO 2025-06-06 00:03:00,882 basehttp 16452 6164819968 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-06 00:03:00,888 basehttp 16452 6181646336 "GET /static/js/milestone_tracker.js HTTP/1.1" 200 20134
INFO 2025-06-06 00:03:05,514 basehttp 16452 6164819968 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 5652
INFO 2025-06-06 00:03:05,519 basehttp 16452 6164819968 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:03:06,289 basehttp 16452 6164819968 "POST /gobeyond/regenerate_milestones/3/ HTTP/1.1" 200 134
INFO 2025-06-06 00:03:08,307 basehttp 16452 6164819968 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:03:09,945 basehttp 16452 6164819968 "GET /gobeyond/ HTTP/1.1" 200 206159
INFO 2025-06-06 00:03:12,364 basehttp 16452 6198472704 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 218
INFO 2025-06-06 00:03:12,375 basehttp 16452 6198472704 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:03:14,403 basehttp 16452 6198472704 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:03:19,423 basehttp 16452 6164819968 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:03:24,437 basehttp 16452 6198472704 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:03:29,456 basehttp 16452 6215299072 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:03:34,468 basehttp 16452 6232125440 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:03:36,432 basehttp 16452 6232125440 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 6209
INFO 2025-06-06 00:03:36,444 basehttp 16452 6232125440 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:03:36,940 basehttp 16452 6232125440 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 6209
INFO 2025-06-06 00:03:36,949 basehttp 16452 6232125440 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:03:39,480 basehttp 16452 6164819968 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:03:44,145 basehttp 16452 6164819968 "POST /gobeyond/regenerate_milestones/2/ HTTP/1.1" 200 134
INFO 2025-06-06 00:03:44,496 basehttp 16452 6164819968 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:03:44,506 basehttp 16452 6164819968 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 4168
INFO 2025-06-06 00:03:44,516 basehttp 16452 6164819968 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:03:46,158 basehttp 16452 6164819968 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:03:51,175 basehttp 16452 6164819968 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:03:56,201 basehttp 16452 6198472704 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:04:01,220 basehttp 16452 6215299072 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:04:06,237 basehttp 16452 6232125440 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:04:11,255 basehttp 16452 6248951808 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:04:16,272 basehttp 16452 6164819968 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:04:21,283 basehttp 16452 6198472704 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:04:26,293 basehttp 16452 6215299072 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:04:31,309 basehttp 16452 6181646336 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:04:31,321 basehttp 16452 6181646336 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 4220
INFO 2025-06-06 00:04:31,330 basehttp 16452 6181646336 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:04:59,953 basehttp 16452 6232125440 "POST /gobeyond/edit_goal/3/ HTTP/1.1" 302 0
INFO 2025-06-06 00:04:59,968 basehttp 16452 6232125440 "GET /gobeyond/ HTTP/1.1" 200 206250
INFO 2025-06-06 00:05:00,005 basehttp 16452 6232125440 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-06 00:05:00,005 basehttp 16452 6248951808 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-06 00:05:00,036 basehttp 16452 6248951808 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-06 00:05:00,037 basehttp 16452 6232125440 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-06 00:05:00,041 basehttp 16452 6232125440 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-06 00:05:02,348 basehttp 16452 6164819968 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 4168
INFO 2025-06-06 00:05:02,357 basehttp 16452 6164819968 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:05,978 basehttp 16452 6164819968 "POST /gobeyond/regenerate_milestones/3/ HTTP/1.1" 200 134
INFO 2025-06-06 00:05:07,994 basehttp 16452 6198472704 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:13,010 basehttp 16452 6164819968 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:17,867 basehttp 16452 6198472704 "POST /gobeyond/edit_goal/2/ HTTP/1.1" 302 0
INFO 2025-06-06 00:05:17,890 basehttp 16452 6198472704 "GET /gobeyond/ HTTP/1.1" 200 206341
INFO 2025-06-06 00:05:19,861 basehttp 16452 6198472704 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 4220
INFO 2025-06-06 00:05:19,868 basehttp 16452 6198472704 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:23,191 basehttp 16452 6164819968 "POST /gobeyond/regenerate_milestones/2/ HTTP/1.1" 200 134
INFO 2025-06-06 00:05:24,045 basehttp 16452 6164819968 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 218
INFO 2025-06-06 00:05:24,052 basehttp 16452 6164819968 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:25,205 basehttp 16452 6164819968 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:26,071 basehttp 16452 6164819968 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:30,213 basehttp 16452 6215299072 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:31,080 basehttp 16452 6215299072 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:35,228 basehttp 16452 6164819968 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:36,090 basehttp 16452 6164819968 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:40,246 basehttp 16452 6181646336 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:41,099 basehttp 16452 6181646336 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:41,106 basehttp 16452 6181646336 "GET /gobeyond/get_milestones/3/ HTTP/1.1" 200 4063
INFO 2025-06-06 00:05:41,112 basehttp 16452 6181646336 "GET /gobeyond/check_milestone_task_status/3/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:45,255 basehttp 16452 6215299072 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:50,264 basehttp 16452 6232125440 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:55,279 basehttp 16452 6198472704 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:05:55,291 basehttp 16452 6198472704 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 3980
INFO 2025-06-06 00:05:55,301 basehttp 16452 6198472704 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:06:49,725 basehttp 16452 6164819968 "POST /gobeyond/archive_goal/2/ HTTP/1.1" 302 0
INFO 2025-06-06 00:06:49,743 basehttp 16452 6164819968 "GET /gobeyond/ HTTP/1.1" 200 113511
INFO 2025-06-06 00:06:49,779 basehttp 16452 6164819968 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-06 00:06:52,059 basehttp 16452 6164819968 "POST /gobeyond/archive_goal/3/ HTTP/1.1" 302 0
INFO 2025-06-06 00:06:52,069 basehttp 16452 6164819968 "GET /gobeyond/ HTTP/1.1" 200 14011
INFO 2025-06-06 00:16:17,540 autoreload 16452 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/models.py changed, reloading.
INFO 2025-06-06 00:16:18,500 autoreload 20659 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:16:31,252 autoreload 20659 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/models.py changed, reloading.
INFO 2025-06-06 00:16:32,049 autoreload 20730 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:17:33,815 autoreload 20730 8619180608 /Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/langsmith/env/_runtime_env.py changed, reloading.
INFO 2025-06-06 00:17:34,636 autoreload 20985 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:18:44,228 autoreload 20985 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-06 00:18:45,152 autoreload 21253 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:19:10,316 autoreload 21253 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-06 00:19:11,225 autoreload 21385 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:19:33,144 autoreload 21385 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/urls.py changed, reloading.
INFO 2025-06-06 00:19:33,920 autoreload 21473 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:21:37,239 embedding_service 22011 8619180608 Generating embeddings for batch 1 (10 records)
INFO 2025-06-06 00:21:37,239 embedding_service 22011 8619180608 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 00:21:40,758 embedding_service 22011 8619180608 Embedding model loaded successfully
INFO 2025-06-06 00:21:41,594 embedding_service 22011 8619180608 Processed 10/15 records
INFO 2025-06-06 00:21:41,594 embedding_service 22011 8619180608 Generating embeddings for batch 2 (5 records)
INFO 2025-06-06 00:21:41,658 embedding_service 22011 8619180608 Processed 15/15 records
INFO 2025-06-06 00:21:41,916 search_chatbot 22011 8619180608 Started new search session 4546cdb4-e2b1-49ed-9835-675ab1caf9d5 for user test_search_user
ERROR 2025-06-06 00:21:41,917 search_chatbot 22011 8619180608 Error processing message: Negative indexing is not supported.
ERROR 2025-06-06 00:21:41,917 search_chatbot 22011 8619180608 Error processing message: Negative indexing is not supported.
ERROR 2025-06-06 00:21:41,917 search_chatbot 22011 8619180608 Error processing message: Negative indexing is not supported.
INFO 2025-06-06 00:21:54,176 autoreload 21473 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:21:54,913 autoreload 22115 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:22:21,114 embedding_service 22277 8619180608 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 00:22:25,287 embedding_service 22277 8619180608 Embedding model loaded successfully
INFO 2025-06-06 00:22:26,190 embedding_service 22277 8619180608 Created embedding for record 231
INFO 2025-06-06 00:22:26,191 search_chatbot 22277 8619180608 Started new search session 23d71016-0e3e-4929-8ca4-9ba8ecc1a25c for user test_chatbot_user
INFO 2025-06-06 00:22:26,211 search_chatbot 22277 8619180608 Classified query as: semantic_search
INFO 2025-06-06 00:22:26,274 search_chatbot 22277 8619180608 Found 1 similar records
INFO 2025-06-06 00:23:20,104 basehttp 22115 6130069504 "GET /gobeyond/ HTTP/1.1" 200 14011
INFO 2025-06-06 00:23:20,142 basehttp 22115 6146895872 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-06 00:23:20,144 basehttp 22115 6130069504 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-06 00:23:20,151 basehttp 22115 6130069504 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-06 00:23:20,155 basehttp 22115 6146895872 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-06 00:23:20,159 basehttp 22115 6130069504 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-06 00:23:20,160 basehttp 22115 6146895872 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-06 00:23:22,978 basehttp 22115 6146895872 "GET /gobeyond/ HTTP/1.1" 200 14314
INFO 2025-06-06 00:23:25,452 basehttp 22115 6130069504 "GET /gobeyond/ HTTP/1.1" 200 14314
INFO 2025-06-06 00:23:26,901 basehttp 22115 6130069504 "GET /gobeyond/?archived=true HTTP/1.1" 200 281701
INFO 2025-06-06 00:23:28,240 basehttp 22115 6130069504 "GET /gobeyond/ HTTP/1.1" 200 14314
INFO 2025-06-06 00:23:29,297 basehttp 22115 6130069504 "GET /gobeyond/search/ HTTP/1.1" 200 21088
INFO 2025-06-06 00:23:29,361 basehttp 22115 6130069504 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 160
INFO 2025-06-06 00:23:34,244 basehttp 22115 6146895872 "GET /gobeyond/search/ HTTP/1.1" 200 21088
INFO 2025-06-06 00:23:34,344 basehttp 22115 6146895872 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 160
INFO 2025-06-06 00:24:02,016 basehttp 22115 6163722240 "GET /gobeyond/search/ HTTP/1.1" 200 21088
INFO 2025-06-06 00:24:02,093 basehttp 22115 6163722240 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 160
INFO 2025-06-06 00:24:06,736 basehttp 22115 6130069504 "GET /gobeyond/search/ HTTP/1.1" 200 21088
INFO 2025-06-06 00:24:06,828 basehttp 22115 6130069504 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 160
INFO 2025-06-06 00:24:29,154 autoreload 23178 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:24:31,808 basehttp 23178 6205616128 "GET / HTTP/1.1" 200 32374
INFO 2025-06-06 00:24:33,976 basehttp 23178 6205616128 "GET /gobeyond/ HTTP/1.1" 200 14314
INFO 2025-06-06 00:24:35,816 basehttp 23178 6205616128 "GET /gobeyond/search/ HTTP/1.1" 200 21088
INFO 2025-06-06 00:24:35,876 basehttp 23178 6205616128 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 160
WARNING 2025-06-06 00:24:46,163 log 23178 6205042688 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 00:24:46,163 basehttp 23178 6205042688 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 00:24:49,157 basehttp 23178 6221869056 "GET /gobeyond/search/ HTTP/1.1" 200 21088
WARNING 2025-06-06 00:24:49,215 log 23178 6221869056 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 00:24:49,216 basehttp 23178 6221869056 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 00:24:49,236 basehttp 23178 6238695424 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-06 00:24:49,416 basehttp 23178 6238695424 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 160
INFO 2025-06-06 00:25:43,621 basehttp 23178 6205042688 "GET /gobeyond/search/ HTTP/1.1" 200 21088
INFO 2025-06-06 00:25:43,752 basehttp 23178 6205042688 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 160
INFO 2025-06-06 00:26:22,957 basehttp 23178 6221869056 "GET / HTTP/1.1" 200 32374
INFO 2025-06-06 00:26:25,459 basehttp 23178 6238695424 "GET /gobeyond/ HTTP/1.1" 200 14314
INFO 2025-06-06 00:26:25,511 basehttp 23178 6238695424 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-06 00:26:28,114 basehttp 23178 6238695424 "GET /gobeyond/?archived=true HTTP/1.1" 200 281701
INFO 2025-06-06 00:26:31,374 basehttp 23178 6205042688 "GET /gobeyond/get_milestones/4/ HTTP/1.1" 200 4996
INFO 2025-06-06 00:26:31,382 basehttp 23178 6205042688 "GET /gobeyond/check_milestone_task_status/4/ HTTP/1.1" 200 212
INFO 2025-06-06 00:26:32,459 basehttp 23178 6205042688 "GET /gobeyond/get_milestones/2/ HTTP/1.1" 200 3980
INFO 2025-06-06 00:26:32,471 basehttp 23178 6205042688 "GET /gobeyond/check_milestone_task_status/2/ HTTP/1.1" 200 212
INFO 2025-06-06 00:26:41,156 embedding_service 23760 8619180608 Generating embeddings for batch 1 (10 records)
INFO 2025-06-06 00:26:41,156 embedding_service 23760 8619180608 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 00:26:45,306 embedding_service 23760 8619180608 Embedding model loaded successfully
INFO 2025-06-06 00:26:46,127 embedding_service 23760 8619180608 Processed 10/17 records
INFO 2025-06-06 00:26:46,127 embedding_service 23760 8619180608 Generating embeddings for batch 2 (7 records)
INFO 2025-06-06 00:26:46,213 embedding_service 23760 8619180608 Processed 17/17 records
INFO 2025-06-06 00:26:46,586 search_chatbot 23760 8619180608 Started new search session d9493519-08da-40f1-9057-65c0710b3c81 for user search_test_user
INFO 2025-06-06 00:26:46,599 search_chatbot 23760 8619180608 Classified query as: semantic_search
INFO 2025-06-06 00:26:46,647 search_chatbot 23760 8619180608 Found 2 similar records
INFO 2025-06-06 00:26:46,650 search_chatbot 23760 8619180608 Classified query as: followup
INFO 2025-06-06 00:26:46,699 search_chatbot 23760 8619180608 Classified query as: semantic_search
INFO 2025-06-06 00:26:46,747 search_chatbot 23760 8619180608 Found 1 similar records
INFO 2025-06-06 00:26:46,749 search_chatbot 23760 8619180608 Classified query as: semantic_search
INFO 2025-06-06 00:26:46,791 search_chatbot 23760 8619180608 Found 1 similar records
INFO 2025-06-06 00:26:46,792 search_chatbot 23760 8619180608 Classified query as: semantic_search
INFO 2025-06-06 00:26:46,836 search_chatbot 23760 8619180608 Found 1 similar records
ERROR 2025-06-06 00:26:46,854 exception 23760 8619180608 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/utils/deprecation.py", line 128, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/http/request.py", line 151, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 00:26:47,383 log 23760 8619180608 Bad Request: /gobeyond/search/
INFO 2025-06-06 00:26:50,476 basehttp 23178 6221869056 "GET / HTTP/1.1" 200 32374
INFO 2025-06-06 00:26:52,199 basehttp 23178 6221869056 "GET /gobeyond/ HTTP/1.1" 200 14314
INFO 2025-06-06 00:26:53,601 basehttp 23178 6238695424 "GET /gobeyond/search/ HTTP/1.1" 200 21214
INFO 2025-06-06 00:26:53,669 search_chatbot 23178 6255521792 Started new search session 3bd1847d-2b97-483e-934f-5bba12a3b831 for user radi
INFO 2025-06-06 00:26:53,670 basehttp 23178 6255521792 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:26:53,672 basehttp 23178 6238695424 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 160
INFO 2025-06-06 00:28:54,289 autoreload 24748 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:29:11,350 autoreload 24874 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:29:24,397 autoreload 24966 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:29:38,824 basehttp 24966 6122860544 "GET /gobeyond/search/ HTTP/1.1" 200 21214
INFO 2025-06-06 00:29:38,906 basehttp 24966 6122860544 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-06 00:29:39,361 basehttp 24966 6122860544 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 160
INFO 2025-06-06 00:29:39,364 search_chatbot 24966 6139686912 Started new search session e494de9f-bf0c-4de3-89d7-0799d9d365ec for user radi
INFO 2025-06-06 00:29:39,365 basehttp 24966 6139686912 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
WARNING 2025-06-06 00:29:39,546 log 24966 6139686912 Not Found: /favicon.ico
WARNING 2025-06-06 00:29:39,547 basehttp 24966 6139686912 "GET /favicon.ico HTTP/1.1" 404 6399
INFO 2025-06-06 00:30:22,681 embedding_service 25240 8619180608 Generating embeddings for batch 1 (10 records)
INFO 2025-06-06 00:30:22,682 embedding_service 25240 8619180608 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 00:30:26,613 embedding_service 25240 8619180608 Embedding model loaded successfully
INFO 2025-06-06 00:30:27,500 embedding_service 25240 8619180608 Processed 10/57 records
INFO 2025-06-06 00:30:27,500 embedding_service 25240 8619180608 Generating embeddings for batch 2 (10 records)
INFO 2025-06-06 00:30:27,721 embedding_service 25240 8619180608 Processed 20/57 records
INFO 2025-06-06 00:30:27,721 embedding_service 25240 8619180608 Generating embeddings for batch 3 (10 records)
INFO 2025-06-06 00:30:28,141 embedding_service 25240 8619180608 Processed 30/57 records
INFO 2025-06-06 00:30:28,141 embedding_service 25240 8619180608 Generating embeddings for batch 4 (10 records)
INFO 2025-06-06 00:30:28,375 embedding_service 25240 8619180608 Processed 40/57 records
INFO 2025-06-06 00:30:28,375 embedding_service 25240 8619180608 Generating embeddings for batch 5 (10 records)
INFO 2025-06-06 00:30:28,598 embedding_service 25240 8619180608 Processed 50/57 records
INFO 2025-06-06 00:30:28,598 embedding_service 25240 8619180608 Generating embeddings for batch 6 (7 records)
INFO 2025-06-06 00:30:28,740 embedding_service 25240 8619180608 Processed 57/57 records
INFO 2025-06-06 00:30:59,493 basehttp 24966 6122860544 "GET /gobeyond/search/ HTTP/1.1" 200 21214
INFO 2025-06-06 00:30:59,744 search_chatbot 24966 6122860544 Started new search session 8afd9f45-5f5d-46bb-9837-864a852d3565 for user radi
INFO 2025-06-06 00:30:59,745 basehttp 24966 6122860544 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:30:59,758 basehttp 24966 6139686912 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:31:04,805 basehttp 23178 6205042688 "GET /gobeyond/search/ HTTP/1.1" 200 21214
INFO 2025-06-06 00:31:04,937 search_chatbot 23178 6221869056 Started new search session 765a102c-809e-4a25-adec-33978265bfad for user radi
INFO 2025-06-06 00:31:04,939 basehttp 23178 6221869056 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:31:04,942 basehttp 23178 6205042688 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:31:18,901 search_chatbot 23178 6238695424 Classified query as: semantic_search
INFO 2025-06-06 00:31:18,902 embedding_service 23178 6238695424 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 00:31:22,373 embedding_service 23178 6238695424 Embedding model loaded successfully
INFO 2025-06-06 00:31:23,165 search_chatbot 23178 6238695424 Found 4 similar records
INFO 2025-06-06 00:31:23,167 basehttp 23178 6238695424 "POST /gobeyond/search/chat/ HTTP/1.1" 200 1168
INFO 2025-06-06 00:32:51,793 search_chatbot 23178 6238351360 Classified query as: semantic_search
INFO 2025-06-06 00:32:52,711 search_chatbot 23178 6238351360 Found 0 similar records
INFO 2025-06-06 00:32:52,713 basehttp 23178 6238351360 "POST /gobeyond/search/chat/ HTTP/1.1" 200 198
INFO 2025-06-06 00:32:58,832 search_chatbot 23178 6238351360 Classified query as: semantic_search
INFO 2025-06-06 00:32:58,991 search_chatbot 23178 6238351360 Found 0 similar records
INFO 2025-06-06 00:32:58,992 basehttp 23178 6238351360 "POST /gobeyond/search/chat/ HTTP/1.1" 200 198
INFO 2025-06-06 00:33:51,304 basehttp 23178 6238351360 "GET /gobeyond/search/ HTTP/1.1" 200 26680
INFO 2025-06-06 00:33:51,560 search_chatbot 23178 6238351360 Started new search session 29d6ec09-a00e-4902-b494-aed8e8c20bbd for user radi
INFO 2025-06-06 00:33:51,562 basehttp 23178 6238351360 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:33:51,569 basehttp 23178 6316650496 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:33:52,691 basehttp 23178 6316650496 "GET /gobeyond/search/ HTTP/1.1" 200 26680
INFO 2025-06-06 00:33:52,812 search_chatbot 23178 6238351360 Started new search session d0aa570d-aaf6-4d76-ad4c-4534dd2bb462 for user radi
INFO 2025-06-06 00:33:52,813 basehttp 23178 6238351360 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:33:52,814 basehttp 23178 6316650496 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:34:04,789 basehttp 23178 6333476864 "GET /gobeyond/search/ HTTP/1.1" 200 30565
INFO 2025-06-06 00:34:04,946 search_chatbot 23178 6333476864 Started new search session 69849444-3be1-410a-96fa-d5f5e93b7083 for user radi
INFO 2025-06-06 00:34:04,951 basehttp 23178 6333476864 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:34:04,954 basehttp 23178 6350303232 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:34:11,921 basehttp 23178 6238351360 "GET /gobeyond/search/ HTTP/1.1" 200 30565
INFO 2025-06-06 00:34:11,996 search_chatbot 23178 6238351360 Started new search session f8bb8f42-a6e1-474f-9574-443c4d865bf7 for user radi
INFO 2025-06-06 00:34:11,997 basehttp 23178 6238351360 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:34:12,003 basehttp 23178 6316650496 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:34:22,622 basehttp 23178 6333476864 "GET /gobeyond/search/ HTTP/1.1" 200 30565
INFO 2025-06-06 00:34:22,713 basehttp 23178 6350303232 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:34:22,715 search_chatbot 23178 6333476864 Started new search session f07a731a-5b07-4e71-88cc-4d692491348f for user radi
INFO 2025-06-06 00:34:22,715 basehttp 23178 6333476864 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:34:23,659 basehttp 23178 6238351360 "GET /gobeyond/search/ HTTP/1.1" 200 32452
INFO 2025-06-06 00:34:23,729 search_chatbot 23178 6316650496 Started new search session 44a405ef-87f6-4f14-9b74-a30f8fc6daf5 for user radi
INFO 2025-06-06 00:34:23,729 basehttp 23178 6316650496 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:34:23,732 basehttp 23178 6238351360 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:34:44,011 basehttp 23178 6333476864 "GET /gobeyond/search/ HTTP/1.1" 200 32550
INFO 2025-06-06 00:34:44,121 search_chatbot 23178 6350303232 Started new search session 120d309c-e9bf-43b6-95f2-69a2343543de for user radi
INFO 2025-06-06 00:34:44,123 basehttp 23178 6350303232 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:34:44,138 basehttp 23178 6333476864 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:34:54,402 basehttp 23178 6238351360 "POST /gobeyond/search/embed-records/ HTTP/1.1" 200 73
INFO 2025-06-06 00:34:54,427 basehttp 23178 6238351360 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:35:12,976 basehttp 23178 6316650496 "GET /gobeyond/search/ HTTP/1.1" 200 34658
INFO 2025-06-06 00:35:13,095 search_chatbot 23178 6316650496 Started new search session 37b1adaa-6717-4ad1-aec7-e7e87c68bff5 for user radi
INFO 2025-06-06 00:35:13,096 basehttp 23178 6316650496 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:35:13,101 basehttp 23178 6333476864 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:35:36,865 basehttp 23178 6350303232 "GET /gobeyond/search/ HTTP/1.1" 200 35200
INFO 2025-06-06 00:35:37,012 search_chatbot 23178 6350303232 Started new search session 61b9292c-a8fb-4d64-8a88-be24060462d2 for user radi
INFO 2025-06-06 00:35:37,014 basehttp 23178 6350303232 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:35:37,020 basehttp 23178 6367129600 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:35:38,915 basehttp 23178 6238351360 "GET /gobeyond/search/ HTTP/1.1" 200 35200
INFO 2025-06-06 00:35:38,986 search_chatbot 23178 6316650496 Started new search session d8cc2421-75e0-4674-933d-3ee8b2e19ec1 for user radi
INFO 2025-06-06 00:35:38,986 basehttp 23178 6316650496 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:35:38,990 basehttp 23178 6238351360 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:35:41,837 basehttp 24966 6156513280 "GET /gobeyond/search/ HTTP/1.1" 200 35200
INFO 2025-06-06 00:35:42,167 search_chatbot 24966 6156513280 Started new search session 1a93cc16-2d92-4f7d-a4e1-3b908d78500e for user radi
INFO 2025-06-06 00:35:42,168 basehttp 24966 6156513280 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:35:42,169 basehttp 24966 6173339648 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:36:11,940 basehttp 24966 6122860544 "GET /gobeyond/search/ HTTP/1.1" 200 35200
INFO 2025-06-06 00:36:12,073 search_chatbot 24966 6139686912 Started new search session 1f26eca7-6bbd-4d38-ac1d-347ac5fc283c for user radi
INFO 2025-06-06 00:36:12,074 basehttp 24966 6139686912 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:36:12,078 basehttp 24966 6122860544 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
WARNING 2025-06-06 00:36:15,537 log 24966 6156513280 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 00:36:15,538 basehttp 24966 6156513280 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 00:36:19,954 basehttp 24966 6173339648 "GET /gobeyond/search/ HTTP/1.1" 200 35200
WARNING 2025-06-06 00:36:20,048 log 24966 6173339648 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 00:36:20,049 basehttp 24966 6173339648 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 00:36:20,245 search_chatbot 24966 6190166016 Started new search session aab07f45-2850-4048-b711-7ef941ae37c2 for user radi
INFO 2025-06-06 00:36:20,246 basehttp 24966 6190166016 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:36:20,250 basehttp 24966 6173339648 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:36:22,307 basehttp 24966 6173339648 "GET /gobeyond/search/ HTTP/1.1" 200 35200
WARNING 2025-06-06 00:36:22,340 log 24966 6173339648 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 00:36:22,341 basehttp 24966 6173339648 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 00:36:22,347 basehttp 24966 6190166016 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-06 00:36:22,799 search_chatbot 24966 6190166016 Started new search session bc2f83b1-ec77-4521-ac16-2c695f8b4f21 for user radi
INFO 2025-06-06 00:36:22,800 basehttp 24966 6190166016 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:36:22,802 basehttp 24966 6173339648 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:36:31,653 search_chatbot 24966 6122860544 Classified query as: semantic_search
INFO 2025-06-06 00:36:31,654 embedding_service 24966 6122860544 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 00:36:35,288 embedding_service 24966 6122860544 Embedding model loaded successfully
INFO 2025-06-06 00:36:36,109 search_chatbot 24966 6122860544 Found 10 similar records
INFO 2025-06-06 00:36:36,116 basehttp 24966 6122860544 "POST /gobeyond/search/chat/ HTTP/1.1" 200 1282
INFO 2025-06-06 00:37:23,351 basehttp 24966 6122860544 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 00:37:23,485 search_chatbot 24966 6408925184 Started new search session 297c18a1-ba50-4a1e-a95c-c6a626d61d79 for user radi
INFO 2025-06-06 00:37:23,485 basehttp 24966 6408925184 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:37:23,502 basehttp 24966 6122860544 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:37:29,031 search_chatbot 24966 6425751552 Classified query as: semantic_search
INFO 2025-06-06 00:37:29,815 search_chatbot 24966 6425751552 Found 10 similar records
INFO 2025-06-06 00:37:29,816 basehttp 24966 6425751552 "POST /gobeyond/search/chat/ HTTP/1.1" 200 1282
INFO 2025-06-06 00:39:21,542 basehttp 24966 6122860544 "POST /gobeyond/search/embed-records/ HTTP/1.1" 200 73
INFO 2025-06-06 00:39:21,580 basehttp 24966 6122860544 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:39:39,718 autoreload 23178 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/models.py changed, reloading.
INFO 2025-06-06 00:39:39,726 autoreload 24966 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/models.py changed, reloading.
INFO 2025-06-06 00:39:41,710 autoreload 27506 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:39:41,710 autoreload 27505 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:39:47,800 autoreload 27506 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 00:39:47,810 autoreload 27505 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 00:39:48,882 autoreload 27552 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:39:48,882 autoreload 27553 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:40:05,143 autoreload 27553 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 00:40:05,159 autoreload 27552 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 00:40:06,225 autoreload 27656 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:40:06,225 autoreload 27655 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:40:30,003 autoreload 27655 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 00:40:30,021 autoreload 27656 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 00:40:31,025 autoreload 27760 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:40:31,025 autoreload 27759 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:40:58,281 autoreload 27760 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 00:40:58,288 autoreload 27759 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 00:40:59,127 autoreload 27882 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:40:59,127 autoreload 27883 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:41:11,268 autoreload 27883 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:41:11,276 autoreload 27882 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:41:12,359 autoreload 27945 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:41:12,359 autoreload 27944 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:41:19,293 autoreload 27944 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:41:19,294 autoreload 27945 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:41:20,023 autoreload 27985 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:41:20,023 autoreload 27986 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:41:41,584 autoreload 27986 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:41:41,593 autoreload 27985 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:41:42,828 autoreload 28092 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:41:42,828 autoreload 28093 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:41:49,659 autoreload 28092 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-06 00:41:49,659 autoreload 28093 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-06 00:41:50,540 autoreload 28144 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:41:50,540 autoreload 28145 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:42:06,739 autoreload 28144 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-06 00:42:06,747 autoreload 28145 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-06 00:42:07,782 autoreload 28229 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:42:07,782 autoreload 28228 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:42:25,983 basehttp 28228 6156513280 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 00:42:26,456 search_chatbot 28228 6173339648 Started new search session fb4820a0-c0bd-4be3-88b3-77e29058b520 for user radi
INFO 2025-06-06 00:42:26,457 basehttp 28228 6173339648 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:42:26,461 basehttp 28228 6156513280 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:42:48,785 basehttp 28228 6156513280 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 00:42:48,934 search_chatbot 28228 6156513280 Started new search session 0528df72-8cc6-44ec-a8d8-506bfb145e1f for user radi
INFO 2025-06-06 00:42:48,936 basehttp 28228 6156513280 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:42:48,938 basehttp 28228 6173339648 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:42:50,361 embedding_service 28228 6173339648 Processing embeddings for user 8: 0 records, 6 goals, 16 milestones
INFO 2025-06-06 00:42:50,361 embedding_service 28228 6173339648 Generating goal embeddings for batch 1 (6 goals)
INFO 2025-06-06 00:42:50,361 embedding_service 28228 6173339648 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 00:42:54,101 embedding_service 28228 6173339648 Embedding model loaded successfully
INFO 2025-06-06 00:42:55,073 embedding_service 28228 6173339648 Processed 6/6 goals
INFO 2025-06-06 00:42:55,073 embedding_service 28228 6173339648 Generating milestone embeddings for batch 1 (16 milestones)
INFO 2025-06-06 00:42:55,253 embedding_service 28228 6173339648 Processed 16/16 milestones
INFO 2025-06-06 00:42:55,254 basehttp 28228 6173339648 "POST /gobeyond/search/embed-records/ HTTP/1.1" 200 151
INFO 2025-06-06 00:42:55,309 basehttp 28228 6173339648 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:42:57,721 basehttp 28228 6173339648 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 00:42:57,804 search_chatbot 28228 6173339648 Started new search session d90d3725-0b4c-4a45-a893-b20a935febf3 for user radi
INFO 2025-06-06 00:42:57,805 basehttp 28228 6173339648 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:42:57,810 basehttp 28228 6360690688 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:42:58,680 basehttp 28228 6173290496 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 00:42:58,740 search_chatbot 28228 6377517056 Started new search session f72ed807-3121-4982-a251-f2623121aa54 for user radi
INFO 2025-06-06 00:42:58,741 basehttp 28228 6377517056 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:42:58,746 basehttp 28228 6173290496 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:42:59,272 basehttp 28228 6173290496 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 00:42:59,344 search_chatbot 28228 6377517056 Started new search session fe6eb896-0011-4b42-b0fb-858186fc1367 for user radi
INFO 2025-06-06 00:42:59,344 basehttp 28228 6377517056 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:42:59,354 basehttp 28228 6173290496 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:42:59,510 basehttp 28228 6173290496 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 00:42:59,568 search_chatbot 28228 6377517056 Started new search session 0aecfafa-e2f5-4fde-8094-197611e8157a for user radi
INFO 2025-06-06 00:42:59,569 basehttp 28228 6377517056 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:42:59,572 basehttp 28228 6173290496 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:43:03,383 search_chatbot 28228 6173290496 Classified query as: semantic_search
INFO 2025-06-06 00:43:03,660 search_chatbot 28228 6173290496 Found 15 similar records
INFO 2025-06-06 00:43:03,662 basehttp 28228 6173290496 "POST /gobeyond/search/chat/ HTTP/1.1" 200 2334
INFO 2025-06-06 00:43:12,617 autoreload 28228 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:43:13,216 autoreload 28229 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:43:14,248 autoreload 28552 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:43:14,248 autoreload 28553 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:43:32,931 autoreload 28553 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:43:32,932 autoreload 28552 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:43:33,879 autoreload 28656 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:43:33,879 autoreload 28657 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:43:50,297 autoreload 28657 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:43:50,318 autoreload 28656 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:43:51,306 autoreload 28745 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:43:51,306 autoreload 28746 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:44:01,362 autoreload 28746 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:44:01,367 autoreload 28745 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:44:02,262 autoreload 28802 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:44:02,262 autoreload 28803 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:44:21,761 autoreload 28802 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:44:21,763 autoreload 28803 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:44:22,835 autoreload 28891 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:44:22,835 autoreload 28892 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:44:42,445 autoreload 28891 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:44:42,454 autoreload 28892 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:44:43,276 autoreload 28987 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:44:43,276 autoreload 28986 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:44:48,153 basehttp 28987 6198587392 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 00:44:48,348 search_chatbot 28987 6215413760 Started new search session 3554a96b-917b-4cab-bdfc-73dfa3540ce9 for user radi
INFO 2025-06-06 00:44:48,350 basehttp 28987 6215413760 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:44:48,356 basehttp 28987 6198587392 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:44:49,597 basehttp 28987 6198587392 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 00:44:49,670 search_chatbot 28987 6198587392 Started new search session 9004bff9-f1d4-4b94-ba0d-064cfdee10e0 for user radi
INFO 2025-06-06 00:44:49,670 basehttp 28987 6198587392 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:44:49,673 basehttp 28987 6215413760 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:44:55,156 search_chatbot 28986 6156578816 Started new search session 99be6b40-76bd-412a-be96-b3a092c265cd for user radi
INFO 2025-06-06 00:44:55,157 basehttp 28986 6156578816 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:44:57,782 search_chatbot 28986 6156578816 Started new search session d0188ae4-87f0-4870-97b1-31654127b1d5 for user radi
INFO 2025-06-06 00:44:57,784 basehttp 28986 6156578816 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:45:34,107 embedding_service 29232 8619180608 Processing embeddings for user 33: 11 records, 2 goals, 3 milestones
INFO 2025-06-06 00:45:34,107 embedding_service 29232 8619180608 Generating embeddings for batch 1 (10 records)
INFO 2025-06-06 00:45:34,107 embedding_service 29232 8619180608 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 00:45:38,250 embedding_service 29232 8619180608 Embedding model loaded successfully
INFO 2025-06-06 00:45:39,095 embedding_service 29232 8619180608 Processed 10/11 records
INFO 2025-06-06 00:45:39,095 embedding_service 29232 8619180608 Generating embeddings for batch 2 (1 records)
INFO 2025-06-06 00:45:39,164 embedding_service 29232 8619180608 Processed 11/11 records
INFO 2025-06-06 00:45:39,164 embedding_service 29232 8619180608 Generating goal embeddings for batch 1 (2 goals)
INFO 2025-06-06 00:45:39,225 embedding_service 29232 8619180608 Processed 2/2 goals
INFO 2025-06-06 00:45:39,225 embedding_service 29232 8619180608 Generating milestone embeddings for batch 1 (3 milestones)
INFO 2025-06-06 00:45:39,293 embedding_service 29232 8619180608 Processed 3/3 milestones
INFO 2025-06-06 00:45:39,579 search_chatbot 29232 8619180608 Started new search session 14efa8d1-b85f-4ec1-aa6a-a2a73499d6d7 for user enhanced_search_test
INFO 2025-06-06 00:45:39,890 search_chatbot 29232 8619180608 Classified query as: semantic_search
INFO 2025-06-06 00:45:39,962 search_chatbot 29232 8619180608 Found 3 similar records
INFO 2025-06-06 00:45:41,418 search_chatbot 29232 8619180608 Classified query as: followup
INFO 2025-06-06 00:45:42,851 search_chatbot 29232 8619180608 Classified query as: followup
INFO 2025-06-06 00:45:44,490 search_chatbot 29232 8619180608 Classified query as: semantic_search
INFO 2025-06-06 00:45:44,571 search_chatbot 29232 8619180608 Found 4 similar records
INFO 2025-06-06 00:45:57,179 basehttp 28987 6198013952 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 00:45:57,606 search_chatbot 28987 6198013952 Started new search session 4dd2974e-3a94-4268-8b66-38ff65528092 for user radi
INFO 2025-06-06 00:45:57,608 basehttp 28987 6198013952 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:45:57,611 basehttp 28987 6232240128 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:46:15,440 basehttp 28986 6156578816 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 00:46:15,606 search_chatbot 28986 6173405184 Started new search session 56e9c374-5fd8-431d-82f1-b88374d3e8fb for user radi
INFO 2025-06-06 00:46:15,607 basehttp 28986 6173405184 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:46:15,609 basehttp 28986 6156578816 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:46:20,142 search_chatbot 28986 6156578816 Classified query as: clarification
INFO 2025-06-06 00:46:21,662 basehttp 28986 6156578816 "POST /gobeyond/search/chat/ HTTP/1.1" 200 1847
INFO 2025-06-06 00:47:58,575 autoreload 28987 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:47:59,034 autoreload 28986 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:47:59,377 autoreload 29844 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:47:59,846 autoreload 29847 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:48:11,807 autoreload 29844 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:48:11,808 autoreload 29847 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:48:12,711 autoreload 29919 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:48:12,711 autoreload 29918 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:48:24,912 autoreload 29918 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:48:24,915 autoreload 29919 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:48:25,802 autoreload 29985 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:48:25,802 autoreload 29986 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:48:39,006 autoreload 29985 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:48:39,011 autoreload 29986 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:48:39,887 autoreload 30047 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:48:39,887 autoreload 30048 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:48:48,068 basehttp 30048 6129561600 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 00:48:48,589 search_chatbot 30048 6146387968 Started new search session f274cb3a-118b-4f44-858c-7fadc5a191e3 for user radi
INFO 2025-06-06 00:48:48,590 basehttp 30048 6146387968 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:48:48,592 basehttp 30048 6129561600 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:49:09,331 embedding_service 30196 8619180608 Processing embeddings for user 34: 2 records, 1 goals, 0 milestones
INFO 2025-06-06 00:49:09,331 embedding_service 30196 8619180608 Generating embeddings for batch 1 (2 records)
INFO 2025-06-06 00:49:09,331 embedding_service 30196 8619180608 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 00:49:13,506 embedding_service 30196 8619180608 Embedding model loaded successfully
INFO 2025-06-06 00:49:14,353 embedding_service 30196 8619180608 Processed 2/2 records
INFO 2025-06-06 00:49:14,353 embedding_service 30196 8619180608 Generating goal embeddings for batch 1 (1 goals)
INFO 2025-06-06 00:49:14,414 embedding_service 30196 8619180608 Processed 1/1 goals
INFO 2025-06-06 00:49:14,416 search_chatbot 30196 8619180608 Started new search session 22410e8e-4d8b-4f2f-bc7e-1f084db250fb for user concise_test_user
INFO 2025-06-06 00:49:14,800 search_chatbot 30196 8619180608 Classified query as: clarification
INFO 2025-06-06 00:49:15,559 search_chatbot 30196 8619180608 Classified query as: semantic_search
INFO 2025-06-06 00:49:15,646 search_chatbot 30196 8619180608 Found 1 similar records
INFO 2025-06-06 00:49:16,538 search_chatbot 30196 8619180608 Classified query as: followup
INFO 2025-06-06 00:49:44,669 basehttp 30047 6200307712 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 00:49:44,812 search_chatbot 30047 6200307712 Started new search session 01dbaf32-34e1-4052-aa9a-8b4b6c954866 for user radi
INFO 2025-06-06 00:49:44,812 basehttp 30047 6200307712 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:49:44,823 basehttp 30047 6217134080 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:49:49,770 search_chatbot 30047 6217134080 Classified query as: clarification
INFO 2025-06-06 00:49:50,248 basehttp 30047 6217134080 "POST /gobeyond/search/chat/ HTTP/1.1" 200 456
INFO 2025-06-06 00:52:05,542 autoreload 30048 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:52:05,776 autoreload 30047 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:52:06,554 autoreload 30896 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:52:06,573 autoreload 30897 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:52:16,815 autoreload 30897 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:52:16,815 autoreload 30896 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:52:17,722 autoreload 30964 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:52:17,723 autoreload 30965 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:52:25,660 autoreload 30965 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:52:25,667 autoreload 30964 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:52:26,431 autoreload 31025 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:52:26,431 autoreload 31026 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:52:42,487 autoreload 31026 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:52:42,493 autoreload 31025 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:52:43,513 autoreload 31123 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:52:43,513 autoreload 31124 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:53:07,141 autoreload 31123 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:53:07,152 autoreload 31124 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:53:07,974 autoreload 31229 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:53:07,974 autoreload 31230 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:53:26,459 autoreload 31230 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:53:26,472 autoreload 31229 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:53:27,320 autoreload 31314 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:53:27,320 autoreload 31315 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:53:39,462 autoreload 31315 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:53:39,474 autoreload 31314 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:53:40,367 autoreload 31377 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:53:40,367 autoreload 31378 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:53:51,513 autoreload 31378 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:53:51,517 autoreload 31377 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:53:52,373 autoreload 31442 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:53:52,373 autoreload 31441 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:54:01,698 embedding_service 31441 6160953344 Processing embeddings for user 8: 0 records, 0 goals, 0 milestones
INFO 2025-06-06 00:54:01,700 basehttp 31441 6160953344 "POST /gobeyond/search/embed-records/ HTTP/1.1" 200 130
INFO 2025-06-06 00:54:01,715 basehttp 31441 6160953344 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:54:03,339 basehttp 31441 6160953344 "GET / HTTP/1.1" 200 32374
INFO 2025-06-06 00:54:06,139 basehttp 31441 6160953344 "GET /gobeyond/ HTTP/1.1" 200 135532
INFO 2025-06-06 00:54:06,166 basehttp 31441 6160953344 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-06 00:54:06,166 basehttp 31441 6177779712 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-06 00:54:06,169 basehttp 31441 6177779712 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-06 00:54:06,170 basehttp 31441 6160953344 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-06 00:54:06,170 basehttp 31441 6194606080 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-06 00:54:06,171 basehttp 31441 6211432448 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-06 00:54:11,433 basehttp 31441 6160953344 "GET /gobeyond/ HTTP/1.1" 200 135532
INFO 2025-06-06 00:54:17,233 basehttp 31441 6177779712 "GET /gobeyond/?archived=true HTTP/1.1" 200 281991
INFO 2025-06-06 00:54:17,891 embedding_service 31566 8619180608 Processing embeddings for user 35: 1 records, 1 goals, 0 milestones
INFO 2025-06-06 00:54:17,891 embedding_service 31566 8619180608 Generating embeddings for batch 1 (1 records)
INFO 2025-06-06 00:54:17,891 embedding_service 31566 8619180608 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 00:54:20,792 basehttp 31441 6177779712 "GET /gobeyond/ HTTP/1.1" 200 135532
INFO 2025-06-06 00:54:21,880 embedding_service 31566 8619180608 Embedding model loaded successfully
INFO 2025-06-06 00:54:22,630 embedding_service 31566 8619180608 Processed 1/1 records
INFO 2025-06-06 00:54:22,631 embedding_service 31566 8619180608 Generating goal embeddings for batch 1 (1 goals)
INFO 2025-06-06 00:54:22,688 embedding_service 31566 8619180608 Processed 1/1 goals
INFO 2025-06-06 00:54:22,690 search_chatbot 31566 8619180608 Started new search session 6463281b-b300-459c-8c69-0ec09d8d54df for user router_test_user
INFO 2025-06-06 00:54:23,530 search_chatbot 31566 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 00:54:24,071 search_chatbot 31566 8619180608 Router decision: needs_search=True, query='Python work'
INFO 2025-06-06 00:54:24,136 search_chatbot 31566 8619180608 Found 1 results for 'Python work'
INFO 2025-06-06 00:54:24,784 search_chatbot 31566 8619180608 Router decision: needs_search=True, query='Django projects'
INFO 2025-06-06 00:54:24,831 search_chatbot 31566 8619180608 Found 2 results for 'Django projects'
INFO 2025-06-06 00:54:25,299 search_chatbot 31566 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 00:54:25,810 search_chatbot 31566 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 00:54:37,660 basehttp 31442 6171586560 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 00:54:38,513 search_chatbot 31442 6188412928 Started new search session 54ec23b8-3f0f-4476-b73e-21d19a936de1 for user radi
INFO 2025-06-06 00:54:38,514 basehttp 31442 6188412928 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:54:38,525 basehttp 31442 6171586560 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:54:51,526 basehttp 31441 6160953344 "GET /gobeyond/ HTTP/1.1" 200 135532
INFO 2025-06-06 00:54:52,763 basehttp 31441 6160953344 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 00:54:52,845 search_chatbot 31441 6177779712 Started new search session e11180f0-768a-4d39-b3b7-7c6ccbbc7113 for user radi
INFO 2025-06-06 00:54:52,845 basehttp 31441 6177779712 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 00:54:52,849 basehttp 31441 6160953344 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 00:54:56,927 search_chatbot 31441 6160953344 Router decision: needs_search=False, query=''
INFO 2025-06-06 00:54:56,929 basehttp 31441 6160953344 "POST /gobeyond/search/chat/ HTTP/1.1" 200 130
INFO 2025-06-06 00:55:04,533 search_chatbot 31441 6160953344 Router decision: needs_search=False, query=''
INFO 2025-06-06 00:55:04,537 basehttp 31441 6160953344 "POST /gobeyond/search/chat/ HTTP/1.1" 200 160
INFO 2025-06-06 00:56:07,380 autoreload 31442 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:56:07,382 autoreload 31441 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:56:08,461 autoreload 32025 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:56:08,461 autoreload 32024 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:56:17,938 autoreload 32025 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:56:17,940 autoreload 32024 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:56:18,961 autoreload 32086 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:56:18,961 autoreload 32085 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:56:27,086 autoreload 32086 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:56:27,088 autoreload 32085 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:56:28,049 autoreload 32140 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:56:28,049 autoreload 32141 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:56:36,636 autoreload 32140 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:56:36,639 autoreload 32141 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:56:37,638 autoreload 32196 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:56:37,638 autoreload 32195 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:56:51,760 autoreload 32196 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:56:51,772 autoreload 32195 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:56:53,356 autoreload 32270 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:56:53,356 autoreload 32269 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:56:59,231 autoreload 32270 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:56:59,244 autoreload 32269 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:57:00,124 autoreload 32320 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:57:00,124 autoreload 32321 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:57:06,636 autoreload 32321 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:57:06,640 autoreload 32320 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:57:07,515 autoreload 32364 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:57:07,515 autoreload 32363 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:57:14,051 autoreload 32364 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:57:14,054 autoreload 32363 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:57:14,853 autoreload 32406 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:57:14,853 autoreload 32407 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:57:37,417 autoreload 32406 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:57:37,424 autoreload 32407 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:57:38,296 autoreload 32500 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:57:38,296 autoreload 32499 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:57:47,365 autoreload 32499 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:57:47,369 autoreload 32500 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:57:48,178 autoreload 32556 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:57:48,178 autoreload 32555 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:58:13,315 embedding_service 32676 8619180608 Processing embeddings for user 36: 1 records, 1 goals, 0 milestones
INFO 2025-06-06 00:58:13,315 embedding_service 32676 8619180608 Generating embeddings for batch 1 (1 records)
INFO 2025-06-06 00:58:13,315 embedding_service 32676 8619180608 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 00:58:17,481 embedding_service 32676 8619180608 Embedding model loaded successfully
INFO 2025-06-06 00:58:18,235 embedding_service 32676 8619180608 Processed 1/1 records
INFO 2025-06-06 00:58:18,235 embedding_service 32676 8619180608 Generating goal embeddings for batch 1 (1 goals)
INFO 2025-06-06 00:58:18,297 embedding_service 32676 8619180608 Processed 1/1 goals
INFO 2025-06-06 00:58:18,299 search_chatbot 32676 8619180608 Started new search session 7d5d9260-ca61-43ba-a53a-91e435b9d033 for user memory_test_user
INFO 2025-06-06 00:58:19,016 search_chatbot 32676 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 00:58:19,712 search_chatbot 32676 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 00:58:20,223 search_chatbot 32676 8619180608 Router decision: needs_search=True, query='Python work'
INFO 2025-06-06 00:58:20,299 search_chatbot 32676 8619180608 Found 1 results for 'Python work'
INFO 2025-06-06 00:58:20,839 search_chatbot 32676 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 00:58:36,241 autoreload 32556 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:58:36,244 autoreload 32555 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:58:37,527 autoreload 32806 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:58:37,527 autoreload 32807 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:58:55,329 autoreload 32806 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:58:56,151 autoreload 32898 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:58:56,347 autoreload 32807 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:58:56,994 autoreload 32901 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:59:12,947 autoreload 32898 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:59:13,440 autoreload 32901 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:59:13,696 autoreload 32979 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:59:14,216 autoreload 32980 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:59:22,672 autoreload 32980 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:59:22,686 autoreload 32979 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 00:59:23,532 autoreload 33055 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:59:23,532 autoreload 33056 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 00:59:49,331 search_chatbot 33192 8619180608 Started new search session a928690b-42a9-478f-a83a-7a941fb4fb1f for user memory_test
INFO 2025-06-06 00:59:50,209 search_chatbot 33192 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 00:59:50,581 search_chatbot 33192 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 00:59:51,257 search_chatbot 33192 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 01:00:00,108 basehttp 33055 6193573888 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 01:00:00,519 search_chatbot 33055 6193573888 Started new search session 17c04b29-315a-4bb4-9fa4-f10ca591cb74 for user radi
INFO 2025-06-06 01:00:00,521 basehttp 33055 6193573888 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 01:00:00,539 basehttp 33055 6210400256 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 01:01:46,635 autoreload 33055 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:01:46,654 autoreload 33056 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:01:47,840 autoreload 33686 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:01:47,840 autoreload 33687 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:02:08,022 search_chatbot 33788 8619180608 Started new search session 62d04aea-4efb-4c15-827c-e0c1bfa9fb7e for user natural_test
INFO 2025-06-06 01:02:08,684 search_chatbot 33788 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 01:02:09,569 search_chatbot 33788 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 01:02:10,319 search_chatbot 33788 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 01:02:10,991 search_chatbot 33788 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 01:02:25,421 basehttp 33687 6122713088 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 01:02:25,804 search_chatbot 33687 6139539456 Started new search session 2dc7127d-9b42-43fc-956e-0a0e08de1a6d for user radi
INFO 2025-06-06 01:02:25,806 basehttp 33687 6139539456 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 01:02:25,808 basehttp 33687 6122713088 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 01:04:29,137 basehttp 33686 6197637120 "GET /gobeyond/search/ HTTP/1.1" 200 35399
INFO 2025-06-06 01:04:29,306 basehttp 33686 6197637120 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 01:04:29,307 search_chatbot 33686 6214463488 Started new search session eee9cd1f-fe19-44d8-894a-a8d60732dc13 for user radi
INFO 2025-06-06 01:04:29,308 basehttp 33686 6214463488 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 01:04:31,808 search_chatbot 33686 6214463488 Router decision: needs_search=False, query=''
INFO 2025-06-06 01:04:32,013 basehttp 33686 6214463488 "POST /gobeyond/search/chat/ HTTP/1.1" 200 198
INFO 2025-06-06 01:04:36,331 search_chatbot 33686 6197637120 Router decision: needs_search=False, query=''
INFO 2025-06-06 01:04:36,629 basehttp 33686 6197637120 "POST /gobeyond/search/chat/ HTTP/1.1" 200 215
INFO 2025-06-06 01:04:37,245 autoreload 33687 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:04:37,455 autoreload 33686 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:04:38,907 autoreload 34393 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:04:38,907 autoreload 34394 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:04:51,384 autoreload 34393 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:04:51,386 autoreload 34394 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:04:52,210 autoreload 34455 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:04:52,210 autoreload 34454 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:05:01,869 autoreload 34455 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:05:01,879 autoreload 34454 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:05:02,700 autoreload 34525 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:05:02,700 autoreload 34524 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:05:16,860 autoreload 34524 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:05:16,866 autoreload 34525 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:05:17,669 autoreload 34603 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:05:17,669 autoreload 34604 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:05:29,852 autoreload 34603 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:05:29,857 autoreload 34604 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:05:30,608 autoreload 34657 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:05:30,608 autoreload 34658 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:05:41,809 autoreload 34657 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:05:41,822 autoreload 34658 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:05:42,647 autoreload 34711 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:05:42,647 autoreload 34712 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:05:49,317 autoreload 34712 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:05:49,321 autoreload 34711 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:05:50,048 autoreload 34744 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:05:50,048 autoreload 34745 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:06:02,894 autoreload 34744 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:06:02,908 autoreload 34745 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 01:06:03,846 autoreload 34801 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:06:03,846 autoreload 34802 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 01:07:29,686 search_chatbot 35117 8619180608 Started new search session e54ef3bf-a8f9-40ae-a246-12b3c398bee1 for user summary_test
INFO 2025-06-06 01:07:30,436 search_chatbot 35117 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 01:07:31,140 search_chatbot 35117 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 01:07:32,061 search_chatbot 35117 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 01:07:33,083 search_chatbot 35117 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 01:07:34,007 search_chatbot 35117 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 01:07:34,344 search_chatbot 35117 8619180608 Updated conversation summary: Here is the updated conversation summary:

Idan, a...
INFO 2025-06-06 01:07:35,442 search_chatbot 35117 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 01:07:36,364 search_chatbot 35117 8619180608 Router decision: needs_search=True, query='where do I work'
INFO 2025-06-06 01:07:36,365 embedding_service 35117 8619180608 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 01:07:40,396 embedding_service 35117 8619180608 Embedding model loaded successfully
INFO 2025-06-06 01:07:41,153 search_chatbot 35117 8619180608 Found 0 results for 'where do I work'
INFO 2025-06-06 01:07:51,888 basehttp 34802 6169948160 "GET /gobeyond/search/ HTTP/1.1" 200 36687
INFO 2025-06-06 01:07:52,347 search_chatbot 34802 6169948160 Started new search session bb61b6f5-c38a-4b22-979e-ebd94fb91c33 for user radi
INFO 2025-06-06 01:07:52,348 basehttp 34802 6169948160 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 01:07:52,351 basehttp 34802 6186774528 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
WARNING 2025-06-06 01:07:52,545 log 34802 6186774528 Not Found: /favicon.ico
WARNING 2025-06-06 01:07:52,545 basehttp 34802 6186774528 "GET /favicon.ico HTTP/1.1" 404 6399
INFO 2025-06-06 07:15:36,764 basehttp 34802 6169948160 "GET /gobeyond/search/ HTTP/1.1" 200 36687
INFO 2025-06-06 07:15:37,371 search_chatbot 34802 6169948160 Started new search session 237d850f-3f45-4b69-b72a-4d35fee1f25b for user radi
INFO 2025-06-06 07:15:37,374 basehttp 34802 6169948160 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 07:15:37,376 basehttp 34802 6186774528 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 07:15:46,377 search_chatbot 34802 6203600896 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:15:46,668 basehttp 34802 6203600896 "POST /gobeyond/search/chat/ HTTP/1.1" 200 179
INFO 2025-06-06 07:15:51,617 search_chatbot 34802 6203600896 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:15:51,998 basehttp 34802 6203600896 "POST /gobeyond/search/chat/ HTTP/1.1" 200 210
INFO 2025-06-06 07:16:12,875 search_chatbot 34802 6169948160 Router decision: needs_search=True, query='accomplishments last year'
INFO 2025-06-06 07:16:12,879 embedding_service 34802 6169948160 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 07:16:16,936 embedding_service 34802 6169948160 Embedding model loaded successfully
INFO 2025-06-06 07:16:17,798 search_chatbot 34802 6169948160 Found 8 results for 'accomplishments last year'
INFO 2025-06-06 07:16:17,800 basehttp 34802 6169948160 "POST /gobeyond/search/chat/ HTTP/1.1" 200 1226
INFO 2025-06-06 07:16:38,474 search_chatbot 34802 6169948160 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:16:38,878 basehttp 34802 6169948160 "POST /gobeyond/search/chat/ HTTP/1.1" 200 442
INFO 2025-06-06 07:16:53,829 search_chatbot 34802 6186774528 Router decision: needs_search=True, query='resume bullet points'
INFO 2025-06-06 07:16:54,344 search_chatbot 34802 6186774528 Updated conversation summary: Here is the updated conversation summary:

Radi, s...
INFO 2025-06-06 07:16:54,955 basehttp 34802 6186774528 "POST /gobeyond/search/chat/ HTTP/1.1" 200 885
INFO 2025-06-06 07:17:04,484 search_chatbot 34802 6169948160 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:17:05,102 basehttp 34802 6169948160 "POST /gobeyond/search/chat/ HTTP/1.1" 200 784
INFO 2025-06-06 07:17:51,995 search_chatbot 34802 6186774528 Router decision: needs_search=True, query='increase productivity'
INFO 2025-06-06 07:17:52,610 search_chatbot 34802 6186774528 Found 6 results for 'increase productivity'
INFO 2025-06-06 07:17:52,612 basehttp 34802 6186774528 "POST /gobeyond/search/chat/ HTTP/1.1" 200 983
INFO 2025-06-06 07:18:20,381 basehttp 34802 6169948160 "GET /gobeyond/search/ HTTP/1.1" 200 36687
INFO 2025-06-06 07:18:20,624 search_chatbot 34802 6186774528 Started new search session a9aa64a4-9f0f-4f11-9f4c-1cb2d951b98c for user radi
INFO 2025-06-06 07:18:20,627 basehttp 34802 6186774528 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 07:18:20,630 basehttp 34802 6169948160 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 07:18:25,820 search_chatbot 34802 6169948160 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:18:26,018 basehttp 34802 6169948160 "POST /gobeyond/search/chat/ HTTP/1.1" 200 173
INFO 2025-06-06 07:18:29,678 search_chatbot 34802 6186774528 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:18:29,837 basehttp 34802 6186774528 "POST /gobeyond/search/chat/ HTTP/1.1" 200 129
INFO 2025-06-06 07:18:31,314 search_chatbot 34802 6186774528 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:18:31,526 basehttp 34802 6186774528 "POST /gobeyond/search/chat/ HTTP/1.1" 200 179
INFO 2025-06-06 07:18:32,853 search_chatbot 34802 6169948160 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:18:33,157 basehttp 34802 6169948160 "POST /gobeyond/search/chat/ HTTP/1.1" 200 205
INFO 2025-06-06 07:18:34,491 search_chatbot 34802 6169948160 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:18:34,797 search_chatbot 34802 6169948160 Updated conversation summary: Here is the updated conversation summary:

Radi in...
INFO 2025-06-06 07:18:35,108 basehttp 34802 6169948160 "POST /gobeyond/search/chat/ HTTP/1.1" 200 297
INFO 2025-06-06 07:18:36,330 search_chatbot 34802 6169948160 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:18:36,644 basehttp 34802 6169948160 "POST /gobeyond/search/chat/ HTTP/1.1" 200 329
INFO 2025-06-06 07:18:56,098 search_chatbot 34802 6169948160 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:18:56,504 basehttp 34802 6169948160 "POST /gobeyond/search/chat/ HTTP/1.1" 200 284
INFO 2025-06-06 07:18:58,042 search_chatbot 34802 6186774528 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:18:58,559 basehttp 34802 6186774528 "POST /gobeyond/search/chat/ HTTP/1.1" 200 298
INFO 2025-06-06 07:19:00,192 search_chatbot 34802 6186774528 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:19:00,604 basehttp 34802 6186774528 "POST /gobeyond/search/chat/ HTTP/1.1" 200 306
WARNING 2025-06-06 07:19:03,700 log 34802 6169948160 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 07:19:03,700 basehttp 34802 6169948160 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 07:19:11,158 basehttp 34802 6186774528 "GET /gobeyond/search/ HTTP/1.1" 200 36687
WARNING 2025-06-06 07:19:11,195 log 34802 6186774528 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 07:19:11,195 basehttp 34802 6186774528 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 07:19:11,420 search_chatbot 34802 6203600896 Started new search session 545b0e4a-a1e9-469a-b067-c1c3e350fce6 for user radi
INFO 2025-06-06 07:19:11,421 basehttp 34802 6203600896 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 07:19:11,425 basehttp 34802 6186774528 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 07:19:13,175 basehttp 34802 6169948160 "GET /gobeyond/search/ HTTP/1.1" 200 36687
WARNING 2025-06-06 07:19:13,223 log 34802 6169948160 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 07:19:13,232 basehttp 34802 6169948160 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 07:19:13,239 basehttp 34802 6186774528 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-06 07:19:13,645 search_chatbot 34802 6186774528 Started new search session 55bf1155-c2ab-4b69-9bdb-54c822a0a426 for user radi
INFO 2025-06-06 07:19:13,646 basehttp 34802 6186774528 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 07:19:13,651 basehttp 34802 6169948160 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 07:19:20,001 search_chatbot 34802 6169948160 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:19:20,368 basehttp 34802 6169948160 "POST /gobeyond/search/chat/ HTTP/1.1" 200 204
INFO 2025-06-06 07:19:22,302 search_chatbot 34802 6186774528 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:19:22,475 basehttp 34802 6186774528 "POST /gobeyond/search/chat/ HTTP/1.1" 200 129
INFO 2025-06-06 07:19:23,951 search_chatbot 34802 6186774528 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:19:24,245 basehttp 34802 6186774528 "POST /gobeyond/search/chat/ HTTP/1.1" 200 200
INFO 2025-06-06 07:19:25,898 search_chatbot 34802 6186774528 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:19:26,203 basehttp 34802 6186774528 "POST /gobeyond/search/chat/ HTTP/1.1" 200 236
INFO 2025-06-06 07:19:27,736 search_chatbot 34802 6171947008 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:19:28,150 search_chatbot 34802 6171947008 Updated conversation summary: Here is the updated conversation summary:

Radi in...
INFO 2025-06-06 07:19:28,554 basehttp 34802 6171947008 "POST /gobeyond/search/chat/ HTTP/1.1" 200 259
INFO 2025-06-06 07:19:31,016 search_chatbot 34802 6171947008 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:19:31,309 basehttp 34802 6171947008 "POST /gobeyond/search/chat/ HTTP/1.1" 200 254
INFO 2025-06-06 07:19:32,958 search_chatbot 34802 6171947008 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:19:33,369 basehttp 34802 6171947008 "POST /gobeyond/search/chat/ HTTP/1.1" 200 219
INFO 2025-06-06 07:19:34,902 search_chatbot 34802 6171947008 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:19:35,217 basehttp 34802 6171947008 "POST /gobeyond/search/chat/ HTTP/1.1" 200 234
INFO 2025-06-06 07:19:37,468 search_chatbot 34802 6171947008 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:19:37,878 basehttp 34802 6171947008 "POST /gobeyond/search/chat/ HTTP/1.1" 200 280
ERROR 2025-06-06 07:20:39,274 exception 97586 8619180608 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/utils/deprecation.py", line 128, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/http/request.py", line 151, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 07:20:41,248 log 97586 8619180608 Bad Request: /gobeyond/search/start-conversation/
INFO 2025-06-06 07:21:41,883 basehttp 34802 6188773376 "GET /gobeyond/search/ HTTP/1.1" 200 36687
INFO 2025-06-06 07:21:42,131 search_chatbot 34802 6390296576 Started new search session f6b24ed1-56ec-41b3-b3de-b7b50aaa0bab for user radi
INFO 2025-06-06 07:21:42,131 basehttp 34802 6390296576 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 07:21:42,136 basehttp 34802 6188773376 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 07:21:57,915 autoreload 34802 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 07:21:58,538 autoreload 34801 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 07:21:59,784 autoreload 97933 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 07:21:59,784 autoreload 97928 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 07:22:23,531 search_chatbot 98036 8619180608 Started new search session 40765701-046a-4699-a598-1c2bf812831f for user memory_trigger_test
INFO 2025-06-06 07:22:24,426 search_chatbot 98036 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:22:24,785 search_chatbot 98036 8619180608 Message count: 1, Is summarizing: False
INFO 2025-06-06 07:22:25,912 search_chatbot 98036 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:22:26,322 search_chatbot 98036 8619180608 Message count: 2, Is summarizing: False
INFO 2025-06-06 07:22:26,934 search_chatbot 98036 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:22:27,349 search_chatbot 98036 8619180608 Message count: 3, Is summarizing: False
INFO 2025-06-06 07:22:27,961 search_chatbot 98036 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:22:28,372 search_chatbot 98036 8619180608 Message count: 4, Is summarizing: False
INFO 2025-06-06 07:22:28,883 search_chatbot 98036 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:22:29,392 search_chatbot 98036 8619180608 Updated conversation summary: Here is the updated conversation summary:

Idan, a...
INFO 2025-06-06 07:22:29,804 search_chatbot 98036 8619180608 Message count: 5, Is summarizing: True
INFO 2025-06-06 07:22:29,804 search_chatbot 98036 8619180608 Added brain emoji to response: 🧠 Hi Idan! I'm doing great, thanks for asking! I'm...
INFO 2025-06-06 07:22:41,401 basehttp 97933 6163542016 "GET /gobeyond/search/ HTTP/1.1" 200 36687
INFO 2025-06-06 07:22:41,675 search_chatbot 97933 6180368384 Started new search session 0f5d0374-6a13-4526-a884-c3545ae82064 for user radi
INFO 2025-06-06 07:22:41,676 basehttp 97933 6180368384 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 07:22:41,677 basehttp 97933 6197194752 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 07:36:56,747 basehttp 97933 6163542016 "GET /gobeyond/search/ HTTP/1.1" 200 36687
INFO 2025-06-06 07:36:56,963 search_chatbot 97933 6197194752 Started new search session 3a5b5ea8-399a-4b6d-b4d7-7fd1b26f607c for user radi
INFO 2025-06-06 07:36:56,964 basehttp 97933 6197194752 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 07:36:56,971 basehttp 97933 6180368384 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 07:37:03,700 search_chatbot 97933 6214021120 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:37:04,098 search_chatbot 97933 6214021120 Message count: 1, Is summarizing: False
INFO 2025-06-06 07:37:04,100 basehttp 97933 6214021120 "POST /gobeyond/search/chat/ HTTP/1.1" 200 192
INFO 2025-06-06 07:37:05,726 search_chatbot 97933 6214021120 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:37:05,952 search_chatbot 97933 6214021120 Message count: 2, Is summarizing: False
INFO 2025-06-06 07:37:05,954 basehttp 97933 6214021120 "POST /gobeyond/search/chat/ HTTP/1.1" 200 129
INFO 2025-06-06 07:37:07,163 search_chatbot 97933 6214021120 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:37:07,411 search_chatbot 97933 6214021120 Message count: 3, Is summarizing: False
INFO 2025-06-06 07:37:07,412 basehttp 97933 6214021120 "POST /gobeyond/search/chat/ HTTP/1.1" 200 176
INFO 2025-06-06 07:37:08,695 search_chatbot 97933 6214021120 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:37:09,116 search_chatbot 97933 6214021120 Message count: 4, Is summarizing: False
INFO 2025-06-06 07:37:09,118 basehttp 97933 6214021120 "POST /gobeyond/search/chat/ HTTP/1.1" 200 253
INFO 2025-06-06 07:37:10,438 search_chatbot 97933 6214021120 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:37:10,834 search_chatbot 97933 6214021120 Updated conversation summary: Here is the updated conversation summary:

Radi, a...
INFO 2025-06-06 07:37:11,171 search_chatbot 97933 6214021120 Message count: 5, Is summarizing: True
INFO 2025-06-06 07:37:11,172 search_chatbot 97933 6214021120 Added brain emoji to response: 🧠 Hi Radi! I think we've got a "hi" streak going o...
INFO 2025-06-06 07:37:11,173 basehttp 97933 6214021120 "POST /gobeyond/search/chat/ HTTP/1.1" 200 269
INFO 2025-06-06 07:37:12,891 search_chatbot 97933 6163542016 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:37:13,205 search_chatbot 97933 6163542016 Message count: 6, Is summarizing: False
INFO 2025-06-06 07:37:13,207 basehttp 97933 6163542016 "POST /gobeyond/search/chat/ HTTP/1.1" 200 218
INFO 2025-06-06 07:37:16,173 search_chatbot 97933 6163542016 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:37:16,581 search_chatbot 97933 6163542016 Message count: 7, Is summarizing: False
INFO 2025-06-06 07:37:16,582 basehttp 97933 6163542016 "POST /gobeyond/search/chat/ HTTP/1.1" 200 225
INFO 2025-06-06 07:37:40,449 basehttp 97933 6163542016 "GET /gobeyond/search/ HTTP/1.1" 200 36687
INFO 2025-06-06 07:37:40,539 search_chatbot 97933 6163542016 Started new search session 2f90c73a-5258-4318-9a96-d66917ee8b41 for user radi
INFO 2025-06-06 07:37:40,539 basehttp 97933 6163542016 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 07:37:40,542 basehttp 97933 6180368384 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 07:37:44,134 search_chatbot 97933 6197194752 Started new search session d569efb1-1b62-42c2-8274-63203f2e484b for user radi
INFO 2025-06-06 07:37:44,135 basehttp 97933 6197194752 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 07:37:45,701 basehttp 97933 6197194752 "GET / HTTP/1.1" 200 32374
INFO 2025-06-06 07:37:45,727 basehttp 97933 6197194752 "GET /static/users/logo.png HTTP/1.1" 200 61827
INFO 2025-06-06 07:37:47,250 basehttp 97933 6163542016 "GET /gobeyond HTTP/1.1" 301 0
INFO 2025-06-06 07:37:47,268 basehttp 97933 6180368384 "GET /gobeyond/ HTTP/1.1" 200 135532
INFO 2025-06-06 07:37:47,285 basehttp 97933 6180368384 "GET /static/js/utils.js HTTP/1.1" 200 5217
INFO 2025-06-06 07:37:47,285 basehttp 97933 6163542016 "GET /static/js/dialog_manager.js HTTP/1.1" 200 2606
INFO 2025-06-06 07:37:47,285 basehttp 97933 6197194752 "GET /static/js/add_record.js HTTP/1.1" 200 3342
INFO 2025-06-06 07:37:47,286 basehttp 97933 6214021120 "GET /static/js/media_handling.js HTTP/1.1" 200 7603
INFO 2025-06-06 07:37:47,286 basehttp 97933 6230847488 "GET /static/js/goal_management.js HTTP/1.1" 200 3089
INFO 2025-06-06 07:37:47,287 basehttp 97933 6247673856 "GET /static/js/milestone_tracker.js HTTP/1.1" 200 20134
INFO 2025-06-06 07:38:04,821 autoreload 2145 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 07:38:52,495 basehttp 2145 6168293376 "GET / HTTP/1.1" 200 32374
WARNING 2025-06-06 07:38:53,086 log 2145 6168293376 Not Found: /favicon.ico
WARNING 2025-06-06 07:38:53,087 basehttp 2145 6168293376 "GET /favicon.ico HTTP/1.1" 404 6399
INFO 2025-06-06 07:51:34,577 basehttp 2145 6168293376 "GET /gobeyond/ HTTP/1.1" 200 135532
INFO 2025-06-06 07:51:34,632 basehttp 2145 6325039104 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-06 07:51:34,633 basehttp 2145 6168293376 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-06 07:51:34,725 basehttp 2145 6168293376 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-06 07:51:34,730 basehttp 2145 6325039104 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-06 07:51:34,732 basehttp 2145 6358691840 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-06 07:51:34,733 basehttp 2145 6341865472 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-06 07:51:35,704 basehttp 2145 6341865472 "GET /gobeyond/search/ HTTP/1.1" 200 36687
INFO 2025-06-06 07:51:35,838 search_chatbot 2145 6358691840 Started new search session 5b787723-bb8e-4016-949b-3fc84fe391f6 for user radi
INFO 2025-06-06 07:51:35,839 basehttp 2145 6358691840 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 07:51:35,842 basehttp 2145 6341865472 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 07:51:41,351 search_chatbot 2145 6168293376 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:51:41,652 search_chatbot 2145 6168293376 Message count: 1, Is summarizing: False
INFO 2025-06-06 07:51:41,653 basehttp 2145 6168293376 "POST /gobeyond/search/chat/ HTTP/1.1" 200 192
INFO 2025-06-06 07:51:42,980 search_chatbot 2145 6168293376 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:51:43,140 search_chatbot 2145 6168293376 Message count: 2, Is summarizing: False
INFO 2025-06-06 07:51:43,141 basehttp 2145 6168293376 "POST /gobeyond/search/chat/ HTTP/1.1" 200 129
INFO 2025-06-06 07:51:44,287 search_chatbot 2145 6168293376 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:51:44,459 search_chatbot 2145 6168293376 Message count: 3, Is summarizing: False
INFO 2025-06-06 07:51:44,460 basehttp 2145 6168293376 "POST /gobeyond/search/chat/ HTTP/1.1" 200 168
INFO 2025-06-06 07:51:45,645 search_chatbot 2145 6168293376 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:51:45,950 search_chatbot 2145 6168293376 Message count: 4, Is summarizing: False
INFO 2025-06-06 07:51:45,952 basehttp 2145 6168293376 "POST /gobeyond/search/chat/ HTTP/1.1" 200 257
INFO 2025-06-06 07:51:47,241 search_chatbot 2145 6168293376 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:51:47,690 search_chatbot 2145 6168293376 Updated conversation summary: Here is the updated conversation summary:

Radi, a...
INFO 2025-06-06 07:51:47,917 search_chatbot 2145 6168293376 Message count: 5, Is summarizing: True
INFO 2025-06-06 07:51:47,918 search_chatbot 2145 6168293376 Added brain emoji to response: 🧠 Hi Radi! I think we've established that we can b...
INFO 2025-06-06 07:51:47,919 basehttp 2145 6168293376 "POST /gobeyond/search/chat/ HTTP/1.1" 200 233
INFO 2025-06-06 07:51:51,988 search_chatbot 2145 6168293376 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:51:52,195 search_chatbot 2145 6168293376 Message count: 6, Is summarizing: False
INFO 2025-06-06 07:51:52,196 basehttp 2145 6168293376 "POST /gobeyond/search/chat/ HTTP/1.1" 200 222
INFO 2025-06-06 07:52:00,595 search_chatbot 2145 6168293376 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:52:01,005 search_chatbot 2145 6168293376 Message count: 7, Is summarizing: False
INFO 2025-06-06 07:52:01,006 basehttp 2145 6168293376 "POST /gobeyond/search/chat/ HTTP/1.1" 200 259
INFO 2025-06-06 07:54:14,533 basehttp 2145 6168293376 "GET /gobeyond/search/ HTTP/1.1" 200 36687
INFO 2025-06-06 07:54:14,811 search_chatbot 2145 6168293376 Started new search session 1f6bc070-ba77-4a35-b4c5-6bcbbe4ec773 for user radi
INFO 2025-06-06 07:54:14,818 basehttp 2145 6168293376 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 07:54:14,841 basehttp 2145 6325039104 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
WARNING 2025-06-06 07:54:17,458 log 2145 6325039104 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 07:54:17,459 basehttp 2145 6325039104 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 07:54:20,424 basehttp 2145 6341865472 "GET /gobeyond/search/ HTTP/1.1" 200 36687
WARNING 2025-06-06 07:54:20,478 log 2145 6341865472 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 07:54:20,478 basehttp 2145 6341865472 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 07:54:20,484 basehttp 2145 6358691840 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-06 07:54:20,700 search_chatbot 2145 6358691840 Started new search session a0b19748-bd58-4563-8275-44401bda3d55 for user radi
INFO 2025-06-06 07:54:20,702 basehttp 2145 6358691840 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 07:54:20,713 basehttp 2145 6341865472 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 07:54:24,029 basehttp 2145 6168293376 "GET /gobeyond/search/ HTTP/1.1" 200 36687
WARNING 2025-06-06 07:54:24,057 log 2145 6168293376 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 07:54:24,057 basehttp 2145 6168293376 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 07:54:24,152 search_chatbot 2145 6325039104 Started new search session 0cad2bcd-1002-46db-a860-55364ce0cc8d for user radi
INFO 2025-06-06 07:54:24,153 basehttp 2145 6325039104 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 07:54:24,158 basehttp 2145 6168293376 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 07:54:26,337 basehttp 2145 6168293376 "GET /gobeyond/search/ HTTP/1.1" 200 36687
WARNING 2025-06-06 07:54:26,373 log 2145 6168293376 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 07:54:26,379 basehttp 2145 6168293376 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 07:54:26,381 basehttp 2145 6325039104 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-06 07:54:26,598 search_chatbot 2145 6325039104 Started new search session 7edd3e10-5c50-4a59-9b0d-e3ae0a6b6a9a for user radi
INFO 2025-06-06 07:54:26,599 basehttp 2145 6325039104 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 07:54:26,601 basehttp 2145 6168293376 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 07:54:30,129 search_chatbot 2145 6168293376 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:54:30,458 search_chatbot 2145 6168293376 Message count: 1, Is summarizing: False
INFO 2025-06-06 07:54:30,459 basehttp 2145 6168293376 "POST /gobeyond/search/chat/ HTTP/1.1" 200 198
INFO 2025-06-06 07:54:35,964 search_chatbot 2145 6325039104 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:54:36,270 search_chatbot 2145 6325039104 Message count: 2, Is summarizing: False
INFO 2025-06-06 07:54:36,271 basehttp 2145 6325039104 "POST /gobeyond/search/chat/ HTTP/1.1" 200 225
INFO 2025-06-06 07:54:37,605 search_chatbot 2145 6325039104 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:54:37,915 search_chatbot 2145 6325039104 Message count: 3, Is summarizing: False
INFO 2025-06-06 07:54:37,917 basehttp 2145 6325039104 "POST /gobeyond/search/chat/ HTTP/1.1" 200 186
INFO 2025-06-06 07:54:39,243 search_chatbot 2145 6325039104 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:54:39,551 search_chatbot 2145 6325039104 Message count: 4, Is summarizing: False
INFO 2025-06-06 07:54:39,552 basehttp 2145 6325039104 "POST /gobeyond/search/chat/ HTTP/1.1" 200 202
INFO 2025-06-06 07:54:40,984 search_chatbot 2145 6325039104 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:54:41,369 search_chatbot 2145 6325039104 Updated conversation summary: Here is the updated conversation summary:

Radi in...
INFO 2025-06-06 07:54:41,705 search_chatbot 2145 6325039104 Message count: 5, Is summarizing: True
INFO 2025-06-06 07:54:41,706 search_chatbot 2145 6325039104 Added brain emoji to response: 🧠 Hi Radi! I'm still here to help you with your da...
INFO 2025-06-06 07:54:41,706 basehttp 2145 6325039104 "POST /gobeyond/search/chat/ HTTP/1.1" 200 222
INFO 2025-06-06 07:54:43,138 search_chatbot 2145 6325039104 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:54:43,414 search_chatbot 2145 6325039104 Message count: 6, Is summarizing: False
INFO 2025-06-06 07:54:43,415 basehttp 2145 6325039104 "POST /gobeyond/search/chat/ HTTP/1.1" 200 196
INFO 2025-06-06 07:54:44,865 search_chatbot 2145 6168293376 Router decision: needs_search=False, query=''
INFO 2025-06-06 07:54:45,174 search_chatbot 2145 6168293376 Message count: 7, Is summarizing: False
INFO 2025-06-06 07:54:45,175 basehttp 2145 6168293376 "POST /gobeyond/search/chat/ HTTP/1.1" 200 270
INFO 2025-06-06 07:55:06,717 autoreload 2145 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 07:55:07,616 autoreload 5993 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 08:04:43,829 basehttp 5993 6202617856 "GET /gobeyond/search/ HTTP/1.1" 200 37473
INFO 2025-06-06 08:04:44,040 search_chatbot 5993 6219444224 Started new search session 55e89985-d29f-40c8-8928-5bdf24a980e4 for user radi
INFO 2025-06-06 08:04:44,041 basehttp 5993 6219444224 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 08:04:44,056 basehttp 5993 6202617856 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 08:04:45,573 basehttp 5993 6202617856 "GET /gobeyond/search/ HTTP/1.1" 200 37473
INFO 2025-06-06 08:04:45,822 search_chatbot 5993 6219444224 Started new search session f8af5deb-d501-4429-a215-eff3bdae56af for user radi
INFO 2025-06-06 08:04:45,822 basehttp 5993 6219444224 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 08:04:45,824 basehttp 5993 6202617856 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 08:05:00,820 basehttp 5993 6202617856 "GET /gobeyond/search/ HTTP/1.1" 200 37473
INFO 2025-06-06 08:05:00,931 basehttp 5993 6202617856 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 08:05:00,932 search_chatbot 5993 6219444224 Started new search session 0d7cb6d9-6209-48db-a109-a39cbcec8915 for user radi
INFO 2025-06-06 08:05:00,933 basehttp 5993 6219444224 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 08:05:08,909 search_chatbot 5993 6236270592 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:05:09,218 search_chatbot 5993 6236270592 Message count: 1, Is summarizing: False
INFO 2025-06-06 08:05:09,220 basehttp 5993 6236270592 "POST /gobeyond/search/chat/ HTTP/1.1" 200 204
INFO 2025-06-06 08:05:11,871 search_chatbot 5993 6236270592 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:05:12,077 search_chatbot 5993 6236270592 Message count: 2, Is summarizing: False
INFO 2025-06-06 08:05:12,078 basehttp 5993 6236270592 "POST /gobeyond/search/chat/ HTTP/1.1" 200 129
INFO 2025-06-06 08:05:13,411 search_chatbot 5993 6236270592 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:05:13,718 search_chatbot 5993 6236270592 Message count: 3, Is summarizing: False
INFO 2025-06-06 08:05:13,719 basehttp 5993 6236270592 "POST /gobeyond/search/chat/ HTTP/1.1" 200 200
INFO 2025-06-06 08:05:14,948 search_chatbot 5993 6236270592 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:05:15,357 search_chatbot 5993 6236270592 Message count: 4, Is summarizing: False
INFO 2025-06-06 08:05:15,359 basehttp 5993 6236270592 "POST /gobeyond/search/chat/ HTTP/1.1" 200 236
INFO 2025-06-06 08:05:18,854 search_chatbot 5993 6202617856 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:05:19,246 search_chatbot 5993 6202617856 Updated conversation summary using last 5 messages: Here is the updated conversation summary:

Radi ha...
INFO 2025-06-06 08:05:19,659 search_chatbot 5993 6202617856 Message count: 5, Is summarizing: True
INFO 2025-06-06 08:05:19,659 search_chatbot 5993 6202617856 Added brain emoji to response: 🧠 Hi Radi! I think we've established that "hi" is ...
INFO 2025-06-06 08:05:19,660 basehttp 5993 6202617856 "POST /gobeyond/search/chat/ HTTP/1.1" 200 310
INFO 2025-06-06 08:05:21,346 search_chatbot 5993 6202617856 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:05:21,708 search_chatbot 5993 6202617856 Message count: 6, Is summarizing: False
INFO 2025-06-06 08:05:21,710 basehttp 5993 6202617856 "POST /gobeyond/search/chat/ HTTP/1.1" 200 242
INFO 2025-06-06 08:06:31,238 search_chatbot 5993 6202617856 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:06:31,645 search_chatbot 5993 6202617856 Message count: 7, Is summarizing: False
INFO 2025-06-06 08:06:31,646 basehttp 5993 6202617856 "POST /gobeyond/search/chat/ HTTP/1.1" 200 261
INFO 2025-06-06 08:06:32,697 search_chatbot 5993 6202617856 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:06:33,181 search_chatbot 5993 6202617856 Message count: 8, Is summarizing: False
INFO 2025-06-06 08:06:33,182 basehttp 5993 6202617856 "POST /gobeyond/search/chat/ HTTP/1.1" 200 217
INFO 2025-06-06 08:06:35,240 search_chatbot 5993 6202617856 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:06:35,537 search_chatbot 5993 6202617856 Message count: 9, Is summarizing: False
INFO 2025-06-06 08:06:35,537 basehttp 5993 6202617856 "POST /gobeyond/search/chat/ HTTP/1.1" 200 221
INFO 2025-06-06 08:06:37,174 search_chatbot 5993 6202617856 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:06:37,647 search_chatbot 5993 6202617856 Updated conversation summary using last 5 messages: Here is the updated conversation summary:

Radi ha...
INFO 2025-06-06 08:06:38,044 search_chatbot 5993 6202617856 Message count: 10, Is summarizing: True
INFO 2025-06-06 08:06:38,044 search_chatbot 5993 6202617856 Added brain emoji to response: 🧠 Hi Radi! I'm happy to see our "hi" streak contin...
INFO 2025-06-06 08:06:38,045 basehttp 5993 6202617856 "POST /gobeyond/search/chat/ HTTP/1.1" 200 228
INFO 2025-06-06 08:07:13,177 basehttp 5993 6202617856 "GET /gobeyond/search/ HTTP/1.1" 200 38478
INFO 2025-06-06 08:07:13,600 search_chatbot 5993 6219444224 Started new search session e7c33a0a-e9bf-470a-a96c-6c5a0deed1df for user radi
INFO 2025-06-06 08:07:13,601 basehttp 5993 6219444224 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 08:07:13,611 basehttp 5993 6202617856 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 08:11:17,341 basehttp 5993 6202617856 "GET /gobeyond/search/ HTTP/1.1" 200 38478
INFO 2025-06-06 08:11:17,512 search_chatbot 5993 6202617856 Started new search session 04ec3c21-ef5d-4c6c-859a-63d6d6dfc120 for user radi
INFO 2025-06-06 08:11:17,513 basehttp 5993 6202617856 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 08:11:17,536 basehttp 5993 6219444224 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 08:11:28,198 search_chatbot 5993 6236270592 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:11:28,416 search_chatbot 5993 6236270592 Message count: 1, Is summarizing: False
INFO 2025-06-06 08:11:28,417 basehttp 5993 6236270592 "POST /gobeyond/search/chat/ HTTP/1.1" 200 192
INFO 2025-06-06 08:11:33,419 search_chatbot 5993 6236270592 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:11:33,625 search_chatbot 5993 6236270592 Message count: 2, Is summarizing: False
INFO 2025-06-06 08:11:33,626 basehttp 5993 6236270592 "POST /gobeyond/search/chat/ HTTP/1.1" 200 207
INFO 2025-06-06 08:11:35,055 search_chatbot 5993 6236270592 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:11:35,221 search_chatbot 5993 6236270592 Message count: 3, Is summarizing: False
INFO 2025-06-06 08:11:35,222 basehttp 5993 6236270592 "POST /gobeyond/search/chat/ HTTP/1.1" 200 129
INFO 2025-06-06 08:11:36,389 search_chatbot 5993 6236270592 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:11:36,589 search_chatbot 5993 6236270592 Message count: 4, Is summarizing: False
INFO 2025-06-06 08:11:36,590 basehttp 5993 6236270592 "POST /gobeyond/search/chat/ HTTP/1.1" 200 168
INFO 2025-06-06 08:11:37,923 search_chatbot 5993 6236270592 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:11:38,333 search_chatbot 5993 6236270592 Updated conversation summary using last 5 messages: Here is the updated conversation summary:

Radi ha...
INFO 2025-06-06 08:11:38,649 search_chatbot 5993 6236270592 Message count: 5, Is summarizing: True
INFO 2025-06-06 08:11:38,649 search_chatbot 5993 6236270592 Added brain emoji to response: 🧠 Hi again Radi! I feel like we're having a great ...
INFO 2025-06-06 08:11:38,650 basehttp 5993 6236270592 "POST /gobeyond/search/chat/ HTTP/1.1" 200 277
INFO 2025-06-06 08:11:53,183 search_chatbot 5993 6202617856 Router decision: needs_search=True, query='resume creation'
INFO 2025-06-06 08:11:53,188 embedding_service 5993 6202617856 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 08:11:57,256 embedding_service 5993 6202617856 Embedding model loaded successfully
INFO 2025-06-06 08:11:58,119 search_chatbot 5993 6202617856 Found 0 results for 'resume creation'
INFO 2025-06-06 08:11:58,121 search_chatbot 5993 6202617856 Message count: 6, Is summarizing: False
INFO 2025-06-06 08:11:58,121 basehttp 5993 6202617856 "POST /gobeyond/search/chat/ HTTP/1.1" 200 125
INFO 2025-06-06 08:12:06,703 search_chatbot 5993 6202617856 Router decision: needs_search=True, query='accomplishment'
INFO 2025-06-06 08:12:06,808 search_chatbot 5993 6202617856 Found 3 results for 'accomplishment'
INFO 2025-06-06 08:12:06,808 search_chatbot 5993 6202617856 Message count: 7, Is summarizing: False
INFO 2025-06-06 08:12:06,809 basehttp 5993 6202617856 "POST /gobeyond/search/chat/ HTTP/1.1" 200 734
INFO 2025-06-06 08:12:13,255 search_chatbot 5993 6202617856 Router decision: needs_search=True, query='accomplishment'
INFO 2025-06-06 08:12:13,352 search_chatbot 5993 6202617856 Found 3 results for 'accomplishment'
INFO 2025-06-06 08:12:13,352 search_chatbot 5993 6202617856 Message count: 8, Is summarizing: False
INFO 2025-06-06 08:12:13,352 basehttp 5993 6202617856 "POST /gobeyond/search/chat/ HTTP/1.1" 200 734
INFO 2025-06-06 08:14:03,056 autoreload 5993 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 08:14:04,843 autoreload 10435 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 08:14:21,596 autoreload 10435 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 08:14:22,342 autoreload 10526 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 08:15:01,001 embedding_service 10726 8619180608 Processing embeddings for user 42: 2 records, 1 goals, 0 milestones
INFO 2025-06-06 08:15:01,001 embedding_service 10726 8619180608 Generating embeddings for batch 1 (2 records)
INFO 2025-06-06 08:15:01,001 embedding_service 10726 8619180608 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 08:15:04,928 embedding_service 10726 8619180608 Embedding model loaded successfully
INFO 2025-06-06 08:15:05,814 embedding_service 10726 8619180608 Processed 2/2 records
INFO 2025-06-06 08:15:05,815 embedding_service 10726 8619180608 Generating goal embeddings for batch 1 (1 goals)
INFO 2025-06-06 08:15:05,870 embedding_service 10726 8619180608 Processed 1/1 goals
INFO 2025-06-06 08:15:05,872 search_chatbot 10726 8619180608 Started new search session 7d3feb78-ab9a-4560-9326-ccd6829cd33c for user llm_search_test
INFO 2025-06-06 08:15:07,142 search_chatbot 10726 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:15:07,357 search_chatbot 10726 8619180608 Message count: 1, Is summarizing: False
INFO 2025-06-06 08:15:07,845 search_chatbot 10726 8619180608 Router decision: needs_search=True, query='Python work'
INFO 2025-06-06 08:15:08,463 search_chatbot 10726 8619180608 Found 2 results for 'Python work'
INFO 2025-06-06 08:15:08,464 search_chatbot 10726 8619180608 Message count: 2, Is summarizing: False
INFO 2025-06-06 08:15:08,971 search_chatbot 10726 8619180608 Router decision: needs_search=True, query='Django'
INFO 2025-06-06 08:15:09,383 search_chatbot 10726 8619180608 Found 2 results for 'Django'
INFO 2025-06-06 08:15:09,384 search_chatbot 10726 8619180608 Message count: 3, Is summarizing: False
INFO 2025-06-06 08:15:21,907 basehttp 10526 6124187648 "GET /gobeyond/search/ HTTP/1.1" 200 38478
INFO 2025-06-06 08:15:22,447 search_chatbot 10526 6124187648 Started new search session a8694c22-222e-4e3c-84d6-7c1af93796b6 for user radi
INFO 2025-06-06 08:15:22,459 basehttp 10526 6124187648 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 08:15:22,496 basehttp 10526 6141014016 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 08:34:13,135 basehttp 10526 6124187648 "GET /gobeyond/search/ HTTP/1.1" 200 38478
INFO 2025-06-06 08:34:13,390 search_chatbot 10526 6141014016 Started new search session 3ec9b64f-79ab-4a93-949d-df78f7160d15 for user radi
INFO 2025-06-06 08:34:13,391 basehttp 10526 6141014016 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 08:34:13,399 basehttp 10526 6157840384 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 08:34:15,555 search_chatbot 10526 6157840384 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:34:15,825 search_chatbot 10526 6157840384 Message count: 1, Is summarizing: False
INFO 2025-06-06 08:34:15,826 basehttp 10526 6157840384 "POST /gobeyond/search/chat/ HTTP/1.1" 200 198
INFO 2025-06-06 08:34:21,155 search_chatbot 10526 6124187648 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:34:21,495 search_chatbot 10526 6124187648 Message count: 2, Is summarizing: False
INFO 2025-06-06 08:34:21,496 basehttp 10526 6124187648 "POST /gobeyond/search/chat/ HTTP/1.1" 200 304
INFO 2025-06-06 08:34:41,665 search_chatbot 10526 6141014016 Router decision: needs_search=True, query='key accomplishments'
INFO 2025-06-06 08:34:41,667 embedding_service 10526 6141014016 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 08:34:46,150 embedding_service 10526 6141014016 Embedding model loaded successfully
INFO 2025-06-06 08:34:48,026 search_chatbot 10526 6141014016 Found 8 results for 'key accomplishments'
INFO 2025-06-06 08:34:48,030 search_chatbot 10526 6141014016 Message count: 3, Is summarizing: False
INFO 2025-06-06 08:34:48,031 basehttp 10526 6141014016 "POST /gobeyond/search/chat/ HTTP/1.1" 200 580
INFO 2025-06-06 08:35:08,763 search_chatbot 10526 6140489728 Router decision: needs_search=True, query='accomplishments and bullet points for resume'
INFO 2025-06-06 08:35:09,425 search_chatbot 10526 6140489728 Found 2 results for 'accomplishments and bullet points for resume'
INFO 2025-06-06 08:35:09,428 search_chatbot 10526 6140489728 Message count: 4, Is summarizing: False
INFO 2025-06-06 08:35:09,428 basehttp 10526 6140489728 "POST /gobeyond/search/chat/ HTTP/1.1" 200 587
INFO 2025-06-06 08:37:26,768 autoreload 10526 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 08:37:28,496 autoreload 15740 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 08:37:39,171 autoreload 15740 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 08:37:39,867 autoreload 15789 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 08:37:52,172 autoreload 15789 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 08:37:52,777 autoreload 15840 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 08:39:23,587 basehttp 15840 6138408960 "GET /gobeyond/search/ HTTP/1.1" 200 43356
INFO 2025-06-06 08:39:24,536 search_chatbot 15840 6155235328 Started new search session b7465d18-dd6a-4418-8cfd-3e5f97390b90 for user radi
INFO 2025-06-06 08:39:24,538 basehttp 15840 6155235328 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 08:39:24,550 basehttp 15840 6138408960 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 08:51:01,185 basehttp 15840 6138408960 "GET /gobeyond/search/ HTTP/1.1" 200 43356
INFO 2025-06-06 08:51:01,453 search_chatbot 15840 6138408960 Started new search session c0fdc741-97fe-4e13-a907-68ca63cf34b9 for user radi
INFO 2025-06-06 08:51:01,469 basehttp 15840 6138408960 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 08:51:01,480 basehttp 15840 6155235328 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 208
INFO 2025-06-06 08:51:15,912 search_chatbot 15840 6172061696 Router decision: needs_search=True, query='tableau'
INFO 2025-06-06 08:51:15,915 embedding_service 15840 6172061696 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 08:51:19,490 embedding_service 15840 6172061696 Embedding model loaded successfully
INFO 2025-06-06 08:51:21,194 search_chatbot 15840 6172061696 Found 8 results for 'tableau'
INFO 2025-06-06 08:51:21,200 search_chatbot 15840 6172061696 Message count: 1, Is summarizing: False
INFO 2025-06-06 08:51:21,201 basehttp 15840 6172061696 "POST /gobeyond/search/chat/ HTTP/1.1" 200 3407
INFO 2025-06-06 08:52:06,057 search_chatbot 15840 6138408960 Router decision: needs_search=False, query=''
INFO 2025-06-06 08:52:06,743 search_chatbot 15840 6138408960 Message count: 2, Is summarizing: False
INFO 2025-06-06 08:52:06,744 basehttp 15840 6138408960 "POST /gobeyond/search/chat/ HTTP/1.1" 200 915
INFO 2025-06-06 09:00:58,047 autoreload 15840 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/models.py changed, reloading.
INFO 2025-06-06 09:00:59,476 autoreload 20878 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 09:01:10,045 autoreload 20878 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 09:01:10,776 autoreload 20933 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 09:01:20,966 autoreload 20933 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 09:01:21,735 autoreload 20979 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 09:01:35,121 autoreload 20979 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 09:01:35,898 autoreload 21043 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 09:01:45,210 autoreload 21043 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 09:01:45,960 autoreload 21083 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 09:01:58,323 autoreload 21083 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 09:01:58,978 autoreload 21146 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 09:02:10,281 autoreload 21146 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 09:02:11,012 autoreload 21192 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 09:02:22,396 autoreload 21192 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 09:02:23,109 autoreload 21246 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 09:02:39,624 autoreload 21246 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 09:02:40,582 autoreload 21312 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 09:03:03,388 autoreload 21312 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 09:03:04,049 autoreload 21398 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 09:03:36,301 autoreload 21398 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-06 09:03:37,162 autoreload 21540 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 09:04:15,852 autoreload 21540 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-06 09:04:16,626 autoreload 21680 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 09:04:28,955 autoreload 21680 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/urls.py changed, reloading.
INFO 2025-06-06 09:04:29,629 autoreload 21731 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 09:06:18,775 basehttp 21731 6135443456 "GET /gobeyond/search/ HTTP/1.1" 200 51161
INFO 2025-06-06 09:06:19,899 search_chatbot 21731 6152269824 Started new search session d2da11e5-18f3-4fd0-8c70-5ab79f361db0 for user radi
INFO 2025-06-06 09:06:19,900 basehttp 21731 6152269824 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 09:06:19,913 basehttp 21731 6135443456 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 286
INFO 2025-06-06 09:58:30,414 basehttp 21731 6135443456 "GET /gobeyond/search/ HTTP/1.1" 200 51161
INFO 2025-06-06 09:58:30,655 search_chatbot 21731 6135443456 Started new search session a611abbc-51f7-4a5b-b632-868751540613 for user radi
INFO 2025-06-06 09:58:30,656 basehttp 21731 6135443456 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 09:58:30,671 basehttp 21731 6169096192 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 286
INFO 2025-06-06 09:58:41,707 search_chatbot 21731 6135443456 Router decision: needs_search=False, query=''
INFO 2025-06-06 09:58:42,105 search_chatbot 21731 6135443456 Message count: 1, Is summarizing: False
INFO 2025-06-06 09:58:42,107 basehttp 21731 6135443456 "POST /gobeyond/search/chat/ HTTP/1.1" 200 203
INFO 2025-06-06 09:59:19,992 search_chatbot 21731 6135443456 Router decision: needs_search=True, query='achievements'
INFO 2025-06-06 09:59:19,996 embedding_service 21731 6135443456 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 09:59:23,660 embedding_service 21731 6135443456 Embedding model loaded successfully
INFO 2025-06-06 09:59:25,217 search_chatbot 21731 6135443456 Found 3 results for 'achievements'
INFO 2025-06-06 09:59:25,221 search_chatbot 21731 6135443456 Message count: 2, Is summarizing: False
INFO 2025-06-06 09:59:25,223 basehttp 21731 6135443456 "POST /gobeyond/search/chat/ HTTP/1.1" 200 1730
INFO 2025-06-06 09:59:54,543 basehttp 21731 6135443456 "GET /gobeyond/search/ HTTP/1.1" 200 51161
INFO 2025-06-06 09:59:54,741 search_chatbot 21731 6135443456 Started new search session 01d6977d-ee5e-4f6a-8832-418feddb2f44 for user radi
INFO 2025-06-06 09:59:54,749 basehttp 21731 6135443456 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 09:59:54,776 basehttp 21731 6152269824 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 286
INFO 2025-06-06 10:01:11,075 autoreload 21731 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/models.py changed, reloading.
INFO 2025-06-06 10:01:12,647 autoreload 34090 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 10:01:25,188 autoreload 34090 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-06 10:01:25,857 autoreload 34149 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 10:01:57,104 autoreload 34149 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-06 10:01:57,783 autoreload 34272 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 10:02:14,445 autoreload 34272 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/views.py changed, reloading.
INFO 2025-06-06 10:02:15,070 autoreload 34343 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 10:02:25,296 autoreload 34343 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/urls.py changed, reloading.
INFO 2025-06-06 10:02:25,907 autoreload 34387 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 10:03:31,841 basehttp 34387 6202880000 "GET /gobeyond/search/ HTTP/1.1" 200 53500
INFO 2025-06-06 10:03:32,432 search_chatbot 34387 6202880000 Started new search session 4f847a7a-80eb-43ae-984d-303899db8813 for user radi
INFO 2025-06-06 10:03:32,435 basehttp 34387 6202880000 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 10:03:32,453 basehttp 34387 6219706368 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 286
INFO 2025-06-06 10:04:02,850 search_chatbot 34787 8619180608 Started new search session ed7519dc-2b92-4c34-8128-aed46e7941af for user async_memory_test
INFO 2025-06-06 10:04:02,854 embedding_service 34787 6180057088 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 10:04:07,033 embedding_service 34787 6180057088 Embedding model loaded successfully
INFO 2025-06-06 10:04:07,812 embedding_service 34787 6180057088 Created embedding for conversation memory 1
INFO 2025-06-06 10:28:49,009 basehttp 34387 6202880000 "GET /gobeyond/search/ HTTP/1.1" 200 53500
INFO 2025-06-06 10:28:49,238 search_chatbot 34387 6219706368 Started new search session d9743190-1246-4179-a02e-5a2bcba0691a for user radi
INFO 2025-06-06 10:28:49,239 basehttp 34387 6219706368 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 10:28:49,254 basehttp 34387 6202880000 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 286
INFO 2025-06-06 10:29:01,847 search_chatbot 34387 6236532736 Router decision: needs_search=False, query=''
INFO 2025-06-06 10:29:02,170 search_chatbot 34387 6236532736 Message count: 1, Is summarizing: False
INFO 2025-06-06 10:29:02,171 basehttp 34387 6236532736 "POST /gobeyond/search/chat/ HTTP/1.1" 200 192
INFO 2025-06-06 10:29:15,581 search_chatbot 34387 6202880000 Router decision: needs_search=True, query='Tableau achievements'
INFO 2025-06-06 10:29:15,587 embedding_service 34387 6202880000 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 10:31:16,208 search_chatbot 34387 6253359104 Started new search session a204ff93-aa94-4b91-80df-fa832e5d6012 for user radi
INFO 2025-06-06 10:31:16,209 basehttp 34387 6253359104 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 10:31:57,223 search_chatbot 40974 8619180608 Started new search session 11a5fc7f-dd2d-496c-90f3-642c0b38cb63 for user router_test
INFO 2025-06-06 10:31:57,805 search_chatbot 40974 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 10:31:58,190 search_chatbot 40974 8619180608 Message count: 1, Is summarizing: False
INFO 2025-06-06 10:31:58,734 search_chatbot 40974 8619180608 Router decision: needs_search=True, query='Tableau achievements'
INFO 2025-06-06 10:31:58,736 embedding_service 40974 8619180608 Loading embedding model: sentence-transformers/all-mpnet-base-v2
INFO 2025-06-06 10:32:15,101 embedding_service 40974 8619180608 Embedding model loaded successfully
INFO 2025-06-06 10:32:15,902 search_chatbot 40974 8619180608 Found 0 results for 'Tableau achievements'
INFO 2025-06-06 10:32:15,905 search_chatbot 40974 8619180608 Message count: 2, Is summarizing: False
INFO 2025-06-06 10:32:16,418 search_chatbot 40974 8619180608 Router decision: needs_search=True, query='Python work'
INFO 2025-06-06 10:32:16,480 search_chatbot 40974 8619180608 Found 0 results for 'Python work'
INFO 2025-06-06 10:32:16,481 search_chatbot 40974 8619180608 Message count: 3, Is summarizing: False
INFO 2025-06-06 10:32:16,923 search_chatbot 40974 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 10:32:17,305 search_chatbot 40974 8619180608 Message count: 4, Is summarizing: False
INFO 2025-06-06 10:32:17,939 search_chatbot 40974 8619180608 Router decision: needs_search=True, query='machine learning projects'
INFO 2025-06-06 10:32:18,366 search_chatbot 40974 8619180608 Updated conversation summary using last 5 messages: Here is the updated conversation summary:

Radi is...
INFO 2025-06-06 10:32:18,753 search_chatbot 40974 8619180608 Message count: 5, Is summarizing: True
INFO 2025-06-06 10:32:18,754 search_chatbot 40974 8619180608 Added brain emoji to response: 🧠 Hi Radi! I'd be happy to help you find your mach...
INFO 2025-06-06 10:32:19,284 search_chatbot 40974 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 10:32:19,924 search_chatbot 40974 8619180608 Message count: 6, Is summarizing: False
INFO 2025-06-06 10:32:25,154 embedding_service 34387 6202880000 Embedding model loaded successfully
INFO 2025-06-06 10:32:26,065 search_chatbot 34387 6202880000 Found 8 results for 'Tableau achievements'
INFO 2025-06-06 10:32:26,071 search_chatbot 34387 6202880000 Message count: 2, Is summarizing: False
INFO 2025-06-06 10:32:26,073 basehttp 34387 6202880000 - Broken pipe from ('127.0.0.1', 63750)
INFO 2025-06-06 10:33:05,527 autoreload 34387 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 10:33:06,783 autoreload 41304 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 10:33:20,440 autoreload 41304 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 10:33:21,255 autoreload 41370 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 11:57:03,163 basehttp 41370 6167621632 "GET / HTTP/1.1" 200 32374
WARNING 2025-06-06 11:57:03,663 log 41370 6167621632 Not Found: /favicon.ico
WARNING 2025-06-06 11:57:03,663 basehttp 41370 6167621632 "GET /favicon.ico HTTP/1.1" 404 6399
INFO 2025-06-06 11:57:04,760 basehttp 41370 6167621632 "GET /gobeyond/ HTTP/1.1" 200 135532
INFO 2025-06-06 11:57:04,784 basehttp 41370 6167621632 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-06 11:57:04,784 basehttp 41370 6184448000 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-06 11:57:04,788 basehttp 41370 6184448000 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-06 11:57:04,789 basehttp 41370 6167621632 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-06 11:57:04,790 basehttp 41370 6201274368 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-06 11:57:04,790 basehttp 41370 6218100736 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-06 11:57:06,489 basehttp 41370 6218100736 "GET /gobeyond/search/ HTTP/1.1" 200 53500
INFO 2025-06-06 11:57:06,578 search_chatbot 41370 6201274368 Started new search session e87b9cc4-5eab-4bae-b88d-28f307a54a05 for user radi
INFO 2025-06-06 11:57:06,578 basehttp 41370 6201274368 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 11:57:06,610 basehttp 41370 6218100736 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 263
INFO 2025-06-06 11:58:07,215 autoreload 41370 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 11:58:08,665 autoreload 60158 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 11:58:23,968 autoreload 60158 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 11:58:24,846 autoreload 60238 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 11:59:27,943 embedding_service 60507 8619180608 Loading Google Generative AI embedding model: models/embedding-001
INFO 2025-06-06 11:59:27,967 embedding_service 60507 8619180608 Google embedding model loaded successfully
INFO 2025-06-06 11:59:43,466 basehttp 60238 ********** "GET /gobeyond/search/ HTTP/1.1" 200 53562
INFO 2025-06-06 11:59:44,456 search_chatbot 60238 ********** Started new search session 9392ed37-fb85-4456-bcec-b34d475d842d for user radi
INFO 2025-06-06 11:59:44,522 basehttp 60238 ********** "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 11:59:44,723 basehttp 60238 6217707520 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 267
INFO 2025-06-06 12:00:05,771 search_chatbot 60238 ********** Router decision: needs_search=True, query='tableau milestones'
INFO 2025-06-06 12:00:05,792 embedding_service 60238 ********** Loading Google Generative AI embedding model: models/embedding-001
INFO 2025-06-06 12:00:05,828 embedding_service 60238 ********** Google embedding model loaded successfully
INFO 2025-06-06 12:00:06,136 search_chatbot 60238 ********** Found 0 results for 'tableau milestones'
INFO 2025-06-06 12:00:06,137 search_chatbot 60238 ********** Message count: 1, Is summarizing: False
INFO 2025-06-06 12:00:06,138 basehttp 60238 ********** "POST /gobeyond/search/chat/ HTTP/1.1" 200 128
INFO 2025-06-06 12:01:16,507 search_chatbot 60987 8619180608 Started new search session e42e7d34-974f-42c4-88e2-0c0b596e140e for user radi
INFO 2025-06-06 12:01:17,029 search_chatbot 60987 8619180608 Router decision: needs_search=True, query='tableau milestones'
INFO 2025-06-06 12:01:17,031 embedding_service 60987 8619180608 Loading Google Generative AI embedding model: models/embedding-001
INFO 2025-06-06 12:01:17,048 embedding_service 60987 8619180608 Google embedding model loaded successfully
INFO 2025-06-06 12:01:17,355 search_chatbot 60987 8619180608 Found 0 results for 'tableau milestones'
INFO 2025-06-06 12:01:17,356 search_chatbot 60987 8619180608 Message count: 1, Is summarizing: False
INFO 2025-06-06 12:01:17,912 search_chatbot 60987 8619180608 Router decision: needs_search=True, query='my Tableau work'
INFO 2025-06-06 12:01:18,171 search_chatbot 60987 8619180608 Found 0 results for 'my Tableau work'
INFO 2025-06-06 12:01:18,172 search_chatbot 60987 8619180608 Message count: 2, Is summarizing: False
INFO 2025-06-06 12:01:18,637 search_chatbot 60987 8619180608 Router decision: needs_search=True, query='achievements on Tableau'
INFO 2025-06-06 12:01:18,892 search_chatbot 60987 8619180608 Found 0 results for 'achievements on Tableau'
INFO 2025-06-06 12:01:18,893 search_chatbot 60987 8619180608 Message count: 3, Is summarizing: False
INFO 2025-06-06 12:01:19,418 search_chatbot 60987 8619180608 Router decision: needs_search=True, query='Tableau projects'
INFO 2025-06-06 12:01:19,640 search_chatbot 60987 8619180608 Found 0 results for 'Tableau projects'
INFO 2025-06-06 12:01:19,641 search_chatbot 60987 8619180608 Message count: 4, Is summarizing: False
INFO 2025-06-06 12:02:07,876 embedding_service 61221 8619180608 Processing embeddings for user 8: 57 records, 6 goals, 16 milestones, 0 conversations
INFO 2025-06-06 12:02:07,876 embedding_service 61221 8619180608 Generating embeddings for batch 1 (10 records)
INFO 2025-06-06 12:02:07,876 embedding_service 61221 8619180608 Loading Google Generative AI embedding model: models/embedding-001
INFO 2025-06-06 12:02:07,884 embedding_service 61221 8619180608 Google embedding model loaded successfully
INFO 2025-06-06 12:02:08,361 embedding_service 61221 8619180608 Processed 10/57 records
INFO 2025-06-06 12:02:08,361 embedding_service 61221 8619180608 Generating embeddings for batch 2 (10 records)
INFO 2025-06-06 12:02:08,829 embedding_service 61221 8619180608 Processed 20/57 records
INFO 2025-06-06 12:02:08,829 embedding_service 61221 8619180608 Generating embeddings for batch 3 (10 records)
INFO 2025-06-06 12:02:09,167 embedding_service 61221 8619180608 Processed 30/57 records
INFO 2025-06-06 12:02:09,167 embedding_service 61221 8619180608 Generating embeddings for batch 4 (10 records)
INFO 2025-06-06 12:02:09,622 embedding_service 61221 8619180608 Processed 40/57 records
INFO 2025-06-06 12:02:09,622 embedding_service 61221 8619180608 Generating embeddings for batch 5 (10 records)
INFO 2025-06-06 12:02:10,131 embedding_service 61221 8619180608 Processed 50/57 records
INFO 2025-06-06 12:02:10,131 embedding_service 61221 8619180608 Generating embeddings for batch 6 (7 records)
INFO 2025-06-06 12:02:10,384 embedding_service 61221 8619180608 Processed 57/57 records
INFO 2025-06-06 12:02:10,384 embedding_service 61221 8619180608 Generating goal embeddings for batch 1 (6 goals)
INFO 2025-06-06 12:02:10,767 embedding_service 61221 8619180608 Processed 6/6 goals
INFO 2025-06-06 12:02:10,767 embedding_service 61221 8619180608 Generating milestone embeddings for batch 1 (10 milestones)
INFO 2025-06-06 12:02:11,126 embedding_service 61221 8619180608 Processed 10/16 milestones
INFO 2025-06-06 12:02:11,126 embedding_service 61221 8619180608 Generating milestone embeddings for batch 2 (6 milestones)
INFO 2025-06-06 12:02:11,490 embedding_service 61221 8619180608 Processed 16/16 milestones
INFO 2025-06-06 12:02:26,528 basehttp 60238 ********** "GET /gobeyond/search/ HTTP/1.1" 200 53562
INFO 2025-06-06 12:02:27,540 search_chatbot 60238 6234533888 Started new search session df229c98-c1ec-497d-8ce2-098e577b8c87 for user radi
INFO 2025-06-06 12:02:27,544 basehttp 60238 6234533888 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:02:27,562 basehttp 60238 6217707520 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:02:51,917 search_chatbot 60238 ********** Router decision: needs_search=True, query='tableau milestones'
INFO 2025-06-06 12:02:53,198 search_chatbot 60238 ********** Found 8 results for 'tableau milestones'
INFO 2025-06-06 12:02:53,200 search_chatbot 60238 ********** Message count: 1, Is summarizing: False
INFO 2025-06-06 12:02:53,200 basehttp 60238 ********** "POST /gobeyond/search/chat/ HTTP/1.1" 200 3330
INFO 2025-06-06 12:03:46,225 search_chatbot 60238 ********** Router decision: needs_search=False, query=''
INFO 2025-06-06 12:03:46,738 search_chatbot 60238 ********** Message count: 2, Is summarizing: False
INFO 2025-06-06 12:03:46,739 basehttp 60238 ********** "POST /gobeyond/search/chat/ HTTP/1.1" 200 670
INFO 2025-06-06 12:04:10,144 search_chatbot 60238 6217707520 Router decision: needs_search=True, query='bullet points for resume related to communication accomplishments'
INFO 2025-06-06 12:04:11,049 search_chatbot 60238 6217707520 Found 8 results for 'bullet points for resume related to communication accomplishments'
INFO 2025-06-06 12:04:11,054 search_chatbot 60238 6217707520 Message count: 3, Is summarizing: False
INFO 2025-06-06 12:04:11,054 basehttp 60238 6217707520 "POST /gobeyond/search/chat/ HTTP/1.1" 200 3571
INFO 2025-06-06 12:05:23,750 basehttp 60238 ********** "GET /gobeyond/search/ HTTP/1.1" 200 53562
INFO 2025-06-06 12:05:24,386 search_chatbot 60238 ********** Started new search session fb51e31d-b1b8-4011-a79f-47a11df3de74 for user radi
INFO 2025-06-06 12:05:24,386 basehttp 60238 ********** "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:05:24,404 basehttp 60238 6217707520 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:06:29,196 search_chatbot 62285 8619180608 Started new search session c8cf93a7-f125-4608-a0df-edbd5444e058 for user radi
ERROR 2025-06-06 12:06:29,208 exception 62285 8619180608 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/utils/deprecation.py", line 128, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/pisakhov-dot-com-mJMwnm1m-py3.13/lib/python3.13/site-packages/django/http/request.py", line 151, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 12:06:30,661 log 62285 8619180608 Bad Request: /gobeyond/search/save-conversation/
INFO 2025-06-06 12:07:43,204 basehttp 60238 ********** "GET /gobeyond/search/ HTTP/1.1" 200 53601
INFO 2025-06-06 12:07:43,892 search_chatbot 60238 6217707520 Started new search session f1690b13-51da-4bbe-bb46-08be8b6e48e1 for user radi
INFO 2025-06-06 12:07:43,914 basehttp 60238 6217707520 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:07:43,932 basehttp 60238 ********** "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:13:43,574 basehttp 60238 6234533888 "GET /gobeyond/search/ HTTP/1.1" 200 53601
INFO 2025-06-06 12:13:43,776 search_chatbot 60238 6234533888 Started new search session 3d45526e-fc81-430c-a963-f7913215add9 for user radi
INFO 2025-06-06 12:13:43,777 basehttp 60238 6234533888 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:13:43,838 basehttp 60238 ********** "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:14:00,708 search_chatbot 60238 ********** Router decision: needs_search=True, query='bullet points for resume'
INFO 2025-06-06 12:14:01,811 search_chatbot 60238 ********** Found 8 results for 'bullet points for resume'
INFO 2025-06-06 12:14:01,812 search_chatbot 60238 ********** Message count: 1, Is summarizing: False
INFO 2025-06-06 12:14:01,812 basehttp 60238 ********** "POST /gobeyond/search/chat/ HTTP/1.1" 200 3599
INFO 2025-06-06 12:14:16,899 search_chatbot 60238 ********** Router decision: needs_search=True, query=''
INFO 2025-06-06 12:14:18,067 search_chatbot 60238 ********** Found 8 results for ''
INFO 2025-06-06 12:14:18,069 search_chatbot 60238 ********** Message count: 2, Is summarizing: False
INFO 2025-06-06 12:14:18,069 basehttp 60238 ********** "POST /gobeyond/search/chat/ HTTP/1.1" 200 3711
INFO 2025-06-06 12:14:27,565 search_chatbot 60238 ********** Router decision: needs_search=True, query='one bullet point'
INFO 2025-06-06 12:14:28,131 search_chatbot 60238 ********** Found 8 results for 'one bullet point'
INFO 2025-06-06 12:14:28,135 search_chatbot 60238 ********** Message count: 3, Is summarizing: False
INFO 2025-06-06 12:14:28,136 basehttp 60238 ********** "POST /gobeyond/search/chat/ HTTP/1.1" 200 2804
WARNING 2025-06-06 12:14:40,017 log 60238 ********** Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 12:14:40,017 basehttp 60238 ********** "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 12:15:25,389 basehttp 60238 6217707520 "GET /gobeyond/search/ HTTP/1.1" 200 53601
INFO 2025-06-06 12:15:25,609 search_chatbot 60238 6217707520 Started new search session aa25a758-2671-4a59-86a7-bc4e152ba9de for user radi
INFO 2025-06-06 12:15:25,610 basehttp 60238 6217707520 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:15:25,621 basehttp 60238 6234533888 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:15:37,664 search_chatbot 60238 ********** Router decision: needs_search=True, query='resume bullet point'
INFO 2025-06-06 12:15:38,501 search_chatbot 60238 ********** Found 8 results for 'resume bullet point'
INFO 2025-06-06 12:15:38,502 search_chatbot 60238 ********** Message count: 1, Is summarizing: False
INFO 2025-06-06 12:15:38,503 basehttp 60238 ********** "POST /gobeyond/search/chat/ HTTP/1.1" 200 3337
INFO 2025-06-06 12:16:52,767 basehttp 60238 ********** "GET /gobeyond/search/ HTTP/1.1" 200 54341
INFO 2025-06-06 12:16:53,363 search_chatbot 60238 ********** Started new search session e0df0a3e-e671-4501-9957-804dcb6a6450 for user radi
INFO 2025-06-06 12:16:53,366 basehttp 60238 ********** "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:16:53,427 basehttp 60238 6217707520 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:18:18,803 search_chatbot 60238 6234533888 Router decision: needs_search=True, query='write me a bullet point for my resume'
INFO 2025-06-06 12:18:19,660 search_chatbot 60238 6234533888 Found 8 results for 'write me a bullet point for my resume'
INFO 2025-06-06 12:18:19,661 search_chatbot 60238 6234533888 Message count: 1, Is summarizing: False
INFO 2025-06-06 12:18:19,662 basehttp 60238 6234533888 "POST /gobeyond/search/chat/ HTTP/1.1" 200 3394
WARNING 2025-06-06 12:18:32,218 log 60238 ********** Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 12:18:32,221 basehttp 60238 ********** "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 12:19:07,962 basehttp 60238 6217707520 "GET /gobeyond/search/ HTTP/1.1" 200 54341
WARNING 2025-06-06 12:19:08,074 log 60238 6217707520 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 12:19:08,074 basehttp 60238 6217707520 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 12:19:08,085 basehttp 60238 6234533888 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-06 12:19:08,299 search_chatbot 60238 6234533888 Started new search session 062e4aaa-181b-4c0f-9821-ece4785552be for user radi
INFO 2025-06-06 12:19:08,300 basehttp 60238 6234533888 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:19:08,345 basehttp 60238 6217707520 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:19:14,112 basehttp 60238 ********** "GET /gobeyond/search/ HTTP/1.1" 200 54341
WARNING 2025-06-06 12:19:14,155 log 60238 ********** Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 12:19:14,157 basehttp 60238 ********** "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 12:19:14,347 search_chatbot 60238 6217707520 Started new search session 5e082347-680e-4297-b272-89d65be58f74 for user radi
INFO 2025-06-06 12:19:14,347 basehttp 60238 6217707520 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:19:14,354 basehttp 60238 ********** "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:19:16,464 basehttp 60238 ********** "GET /gobeyond/search/ HTTP/1.1" 200 54341
WARNING 2025-06-06 12:19:16,505 log 60238 ********** Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 12:19:16,507 basehttp 60238 ********** "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 12:19:16,518 basehttp 60238 6217707520 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-06 12:19:16,708 search_chatbot 60238 6217707520 Started new search session 16c6db4b-60b5-4dfe-a54b-7f4b0e72ee82 for user radi
INFO 2025-06-06 12:19:16,708 basehttp 60238 6217707520 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:19:16,712 basehttp 60238 ********** "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:19:21,300 search_chatbot 60238 ********** Router decision: needs_search=True, query='communication accomplishments'
INFO 2025-06-06 12:19:22,164 search_chatbot 60238 ********** Found 8 results for 'communication accomplishments'
INFO 2025-06-06 12:19:22,166 search_chatbot 60238 ********** Message count: 1, Is summarizing: False
INFO 2025-06-06 12:19:22,167 basehttp 60238 ********** "POST /gobeyond/search/chat/ HTTP/1.1" 200 3632
INFO 2025-06-06 12:20:38,757 basehttp 60238 ********** "GET /gobeyond/search/ HTTP/1.1" 200 56033
INFO 2025-06-06 12:20:39,740 search_chatbot 60238 ********** Started new search session 5dd32038-508e-4aaf-b277-c7a2510d6445 for user radi
INFO 2025-06-06 12:20:39,742 basehttp 60238 ********** "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:20:39,838 basehttp 60238 6217707520 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
WARNING 2025-06-06 12:39:47,794 log 60238 6234533888 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 12:39:47,795 basehttp 60238 6234533888 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 12:39:50,322 basehttp 60238 ********** "GET /gobeyond/search/ HTTP/1.1" 200 56033
WARNING 2025-06-06 12:39:50,365 log 60238 ********** Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 12:39:50,367 basehttp 60238 ********** "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 12:39:50,376 basehttp 60238 ********** "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-06 12:39:50,613 search_chatbot 60238 ********** Started new search session 26ca015a-a99e-4316-a583-fc35fabf352f for user radi
INFO 2025-06-06 12:39:50,615 basehttp 60238 ********** "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:39:50,655 basehttp 60238 ********** "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:39:54,275 basehttp 60238 ********** "GET /gobeyond/search/ HTTP/1.1" 200 56033
WARNING 2025-06-06 12:39:54,333 log 60238 ********** Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 12:39:54,333 basehttp 60238 ********** "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 12:39:54,524 search_chatbot 60238 ********** Started new search session ddb0eac2-2c80-4ab9-a6ac-22a9693f2aea for user radi
INFO 2025-06-06 12:39:54,524 basehttp 60238 ********** "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:39:54,553 basehttp 60238 ********** "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:39:56,486 basehttp 60238 ********** "GET /gobeyond/search/ HTTP/1.1" 200 56033
WARNING 2025-06-06 12:39:56,538 log 60238 ********** Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 12:39:56,538 basehttp 60238 ********** "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 12:39:56,541 basehttp 60238 6217707520 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-06 12:39:56,723 search_chatbot 60238 6217707520 Started new search session c9bf7619-430e-4354-a8ed-27bdb2fbb298 for user radi
INFO 2025-06-06 12:39:56,724 basehttp 60238 6217707520 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:39:56,733 basehttp 60238 ********** "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:40:01,270 search_chatbot 60238 6234533888 Router decision: needs_search=True, query='communication accomplishments'
INFO 2025-06-06 12:40:02,628 search_chatbot 60238 6234533888 Found 8 results for 'communication accomplishments'
INFO 2025-06-06 12:40:02,629 search_chatbot 60238 6234533888 Message count: 1, Is summarizing: False
INFO 2025-06-06 12:40:02,629 basehttp 60238 6234533888 "POST /gobeyond/search/chat/ HTTP/1.1" 200 3687
WARNING 2025-06-06 12:41:00,165 log 60238 ********** Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 12:41:00,166 basehttp 60238 ********** "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 12:41:48,555 basehttp 60238 6217707520 "GET /gobeyond/search/ HTTP/1.1" 200 56033
WARNING 2025-06-06 12:41:48,654 log 60238 6234533888 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 12:41:48,654 basehttp 60238 6234533888 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 12:41:48,661 basehttp 60238 6217707520 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-06 12:41:48,857 search_chatbot 60238 6217707520 Started new search session c9bb15b3-afbc-4de4-94b2-6bf78cd1f72e for user radi
INFO 2025-06-06 12:41:48,858 basehttp 60238 6217707520 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:41:48,867 basehttp 60238 6234533888 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:42:32,521 basehttp 60238 ********** "GET /gobeyond/search/ HTTP/1.1" 200 56468
INFO 2025-06-06 12:42:32,767 search_chatbot 60238 6217707520 Started new search session e00ba409-9e41-4cdb-adfa-7deb6dba73eb for user radi
INFO 2025-06-06 12:42:32,768 basehttp 60238 6217707520 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:42:32,777 basehttp 60238 ********** "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:43:42,686 search_chatbot 60238 ********** Router decision: needs_search=True, query='write bullet points for resume related to communication accomplishments'
INFO 2025-06-06 12:43:43,798 search_chatbot 60238 ********** Found 8 results for 'write bullet points for resume related to communication accomplishments'
INFO 2025-06-06 12:43:43,799 search_chatbot 60238 ********** Message count: 1, Is summarizing: False
INFO 2025-06-06 12:43:43,799 basehttp 60238 ********** "POST /gobeyond/search/chat/ HTTP/1.1" 200 3717
WARNING 2025-06-06 12:43:45,011 log 60238 ********** Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-06-06 12:43:45,011 basehttp 60238 ********** "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 6510
INFO 2025-06-06 12:43:53,739 basehttp 60238 6217707520 "POST /gobeyond/search/save-conversation/ HTTP/1.1" 200 138
INFO 2025-06-06 12:43:53,754 basehttp 60238 6217707520 "GET /gobeyond/search/conversation-task/ffbc8104-c2d4-4c3c-8894-b515034d279a/ HTTP/1.1" 200 189
INFO 2025-06-06 12:43:53,946 embedding_service 60238 6234533888 Created embedding for conversation memory 2
INFO 2025-06-06 12:43:53,949 views 60238 6234533888 Saved conversation memory 2 for user radi
INFO 2025-06-06 12:43:54,776 basehttp 60238 6217707520 "GET /gobeyond/search/conversation-task/ffbc8104-c2d4-4c3c-8894-b515034d279a/ HTTP/1.1" 200 490
INFO 2025-06-06 12:43:54,789 basehttp 60238 6217707520 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:44:50,048 basehttp 60238 ********** "GET /gobeyond/search/ HTTP/1.1" 200 56468
INFO 2025-06-06 12:44:50,268 search_chatbot 60238 ********** Started new search session ff87de33-8596-49b3-8e91-ef10ee1d95ea for user radi
INFO 2025-06-06 12:44:50,269 basehttp 60238 ********** "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:44:50,278 basehttp 60238 ********** "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
ERROR 2025-06-06 12:44:54,044 search_chatbot 60238 ********** Error in query routing: Error code: 400 - {'error': {'message': "Failed to call a function. Please adjust your prompt. See 'failed_generation' for more details.", 'type': 'invalid_request_error', 'code': 'tool_use_failed', 'failed_generation': '<tool-use>{"tool_calls": [{"type": "function", "function": {"name": "QueryRouter"}, "id": "pending", "parameters": {"needs_search": True, "reasoning": "User is asking to write bullet points for resume related to communication accomplishments"}}}]}</tool-use>'}}
INFO 2025-06-06 12:44:54,658 search_chatbot 60238 ********** Message count: 1, Is summarizing: False
INFO 2025-06-06 12:44:54,659 basehttp 60238 ********** "POST /gobeyond/search/chat/ HTTP/1.1" 200 957
INFO 2025-06-06 12:45:13,496 search_chatbot 60238 ********** Started new search session b26ff6c2-02b1-4e6e-8623-bf7c965bc7a3 for user radi
INFO 2025-06-06 12:45:13,497 basehttp 60238 ********** "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:45:16,057 search_chatbot 60238 6217707520 Router decision: needs_search=True, query='communication accomplishments'
INFO 2025-06-06 12:45:16,999 search_chatbot 60238 6217707520 Found 8 results for 'communication accomplishments'
INFO 2025-06-06 12:45:17,004 search_chatbot 60238 6217707520 Message count: 1, Is summarizing: False
INFO 2025-06-06 12:45:17,005 basehttp 60238 6217707520 "POST /gobeyond/search/chat/ HTTP/1.1" 200 3687
INFO 2025-06-06 12:46:32,061 search_chatbot 60238 ********** Router decision: needs_search=True, query='more'
INFO 2025-06-06 12:46:32,960 search_chatbot 60238 ********** Found 8 results for 'more'
INFO 2025-06-06 12:46:32,961 search_chatbot 60238 ********** Message count: 2, Is summarizing: False
INFO 2025-06-06 12:46:32,961 basehttp 60238 ********** "POST /gobeyond/search/chat/ HTTP/1.1" 200 3578
INFO 2025-06-06 12:46:54,767 search_chatbot 60238 ********** Router decision: needs_search=True, query='bullet point in my history'
INFO 2025-06-06 12:46:55,750 search_chatbot 60238 ********** Found 8 results for 'bullet point in my history'
INFO 2025-06-06 12:46:55,751 search_chatbot 60238 ********** Message count: 3, Is summarizing: False
INFO 2025-06-06 12:46:55,752 basehttp 60238 ********** "POST /gobeyond/search/chat/ HTTP/1.1" 200 3640
INFO 2025-06-06 12:48:38,497 autoreload 60238 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 12:48:42,067 autoreload 71927 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 12:48:49,644 autoreload 71927 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 12:48:50,458 autoreload 71962 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 12:48:59,460 basehttp 71962 6190346240 "GET /gobeyond/search/ HTTP/1.1" 200 54895
INFO 2025-06-06 12:49:00,143 search_chatbot 71962 6207172608 Started new search session 802fef1d-f039-4ddb-95e3-f4c2a57bcfe1 for user radi
INFO 2025-06-06 12:49:00,146 basehttp 71962 6207172608 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:49:00,168 basehttp 71962 6190346240 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:54:50,236 search_chatbot 71962 6190346240 Router decision: needs_search=True, query='communication accomplishments'
INFO 2025-06-06 12:54:50,238 embedding_service 71962 6190346240 Loading Google Generative AI embedding model: models/embedding-001
INFO 2025-06-06 12:54:50,277 embedding_service 71962 6190346240 Google embedding model loaded successfully
ERROR 2025-06-06 12:54:50,668 search_chatbot 71962 6190346240 Error in semantic search: 'goal'
INFO 2025-06-06 12:54:50,669 search_chatbot 71962 6190346240 Message count: 1, Is summarizing: False
INFO 2025-06-06 12:54:50,669 basehttp 71962 6190346240 "POST /gobeyond/search/chat/ HTTP/1.1" 200 119
INFO 2025-06-06 12:54:52,954 basehttp 71962 6190346240 "GET /gobeyond/search/ HTTP/1.1" 200 54895
INFO 2025-06-06 12:54:53,169 search_chatbot 71962 6207172608 Started new search session 6f4e81c0-4a7e-42e4-b3ea-ac63dd1147bb for user radi
INFO 2025-06-06 12:54:53,170 basehttp 71962 6207172608 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:54:53,178 basehttp 71962 6190346240 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:54:55,103 search_chatbot 71962 6190346240 Router decision: needs_search=True, query='communication accomplishments'
ERROR 2025-06-06 12:54:55,362 search_chatbot 71962 6190346240 Error in semantic search: 'goal'
INFO 2025-06-06 12:54:55,363 search_chatbot 71962 6190346240 Message count: 1, Is summarizing: False
INFO 2025-06-06 12:54:55,364 basehttp 71962 6190346240 "POST /gobeyond/search/chat/ HTTP/1.1" 200 119
INFO 2025-06-06 12:55:07,538 basehttp 71962 6190346240 "GET /gobeyond/search/ HTTP/1.1" 200 54895
INFO 2025-06-06 12:55:07,741 search_chatbot 71962 6207172608 Started new search session d59d74d4-7b58-4ed0-8e07-dc80431ac2f2 for user radi
INFO 2025-06-06 12:55:07,741 basehttp 71962 6207172608 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 12:55:07,757 basehttp 71962 6190346240 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 12:55:10,465 search_chatbot 71962 6190346240 Router decision: needs_search=True, query='communication accomplishments'
ERROR 2025-06-06 12:55:10,726 search_chatbot 71962 6190346240 Error in semantic search: 'goal'
INFO 2025-06-06 12:55:10,727 search_chatbot 71962 6190346240 Message count: 1, Is summarizing: False
INFO 2025-06-06 12:55:10,727 basehttp 71962 6190346240 "POST /gobeyond/search/chat/ HTTP/1.1" 200 119
INFO 2025-06-06 12:55:43,995 search_chatbot 71962 6190346240 Router decision: needs_search=True, query='tableau accomplishments'
INFO 2025-06-06 12:55:45,081 search_chatbot 71962 6190346240 Found 8 results for 'tableau accomplishments'
INFO 2025-06-06 12:55:45,081 search_chatbot 71962 6190346240 Message count: 2, Is summarizing: False
INFO 2025-06-06 12:55:45,082 basehttp 71962 6190346240 "POST /gobeyond/search/chat/ HTTP/1.1" 200 3477
INFO 2025-06-06 12:55:59,074 search_chatbot 71962 6190346240 Router decision: needs_search=True, query='communication accomplishments'
ERROR 2025-06-06 12:55:59,711 search_chatbot 71962 6190346240 Error in semantic search: 'goal'
INFO 2025-06-06 12:55:59,711 search_chatbot 71962 6190346240 Message count: 3, Is summarizing: False
INFO 2025-06-06 12:55:59,712 basehttp 71962 6190346240 "POST /gobeyond/search/chat/ HTTP/1.1" 200 119
INFO 2025-06-06 13:02:02,927 autoreload 71962 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 13:02:06,609 autoreload 75170 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 13:03:03,392 embedding_service 75428 8619180608 Loading Google Generative AI embedding model: models/embedding-001
INFO 2025-06-06 13:03:03,419 embedding_service 75428 8619180608 Google embedding model loaded successfully
INFO 2025-06-06 13:05:00,599 autoreload 75170 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 13:05:01,532 autoreload 75890 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 16:55:39,536 basehttp 75890 6195572736 "GET /gobeyond/search/ HTTP/1.1" 200 54895
INFO 2025-06-06 16:55:40,064 search_chatbot 75890 6212399104 Started new search session a1e007a4-203a-47c4-8a3b-c49b6611637a for user radi
INFO 2025-06-06 16:55:40,064 basehttp 75890 6212399104 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 16:55:40,080 basehttp 75890 6195572736 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 17:47:36,983 search_chatbot 75890 6195572736 Router decision: needs_search=True, query='communication results'
INFO 2025-06-06 17:47:36,985 embedding_service 75890 6195572736 Loading Google Generative AI embedding model: models/embedding-001
INFO 2025-06-06 17:47:37,030 embedding_service 75890 6195572736 Google embedding model loaded successfully
INFO 2025-06-06 17:47:38,549 search_chatbot 75890 6195572736 Found 8 results for 'communication results'
INFO 2025-06-06 17:47:38,552 search_chatbot 75890 6195572736 Message count: 1, Is summarizing: False
INFO 2025-06-06 17:47:38,553 basehttp 75890 6195572736 "POST /gobeyond/search/chat/ HTTP/1.1" 200 3818
INFO 2025-06-06 17:48:48,807 autoreload 75890 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 17:48:51,830 autoreload 84184 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 17:48:54,944 basehttp 84184 6133067776 "GET /gobeyond/search/ HTTP/1.1" 200 54895
INFO 2025-06-06 17:48:56,059 search_chatbot 84184 6149894144 Started new search session 3f1255fe-f464-4d68-b4dd-221d0865955c for user radi
INFO 2025-06-06 17:48:56,062 basehttp 84184 6149894144 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 17:48:56,082 basehttp 84184 6133067776 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 17:51:26,296 search_chatbot 84184 6132494336 Router decision: needs_search=True, query='communication accomplishments'
INFO 2025-06-06 17:51:26,297 embedding_service 84184 6132494336 Loading Google Generative AI embedding model: models/embedding-001
INFO 2025-06-06 17:51:26,339 embedding_service 84184 6132494336 Google embedding model loaded successfully
INFO 2025-06-06 17:51:27,515 search_chatbot 84184 6132494336 Found 8 results for 'communication accomplishments'
INFO 2025-06-06 17:51:27,517 search_chatbot 84184 6132494336 Message count: 1, Is summarizing: False
INFO 2025-06-06 17:51:27,518 basehttp 84184 6132494336 "POST /gobeyond/search/chat/ HTTP/1.1" 200 3974
INFO 2025-06-06 17:52:04,713 autoreload 84184 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 17:52:07,839 autoreload 84935 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 17:52:25,997 basehttp 84935 6194688000 "GET /gobeyond/search/ HTTP/1.1" 200 54895
INFO 2025-06-06 17:52:26,411 search_chatbot 84935 6211514368 Started new search session 8654f975-1dfc-4443-9165-97a9bfe51979 for user radi
INFO 2025-06-06 17:52:26,415 basehttp 84935 6211514368 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 17:52:26,434 basehttp 84935 6194688000 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 248
INFO 2025-06-06 17:53:42,908 search_chatbot 84935 6194688000 Router decision: needs_search=True, query='communication accomplishments'
INFO 2025-06-06 17:53:42,910 embedding_service 84935 6194688000 Loading Google Generative AI embedding model: models/embedding-001
INFO 2025-06-06 17:53:42,941 embedding_service 84935 6194688000 Google embedding model loaded successfully
INFO 2025-06-06 17:53:44,119 search_chatbot 84935 6194688000 Found 8 results for 'communication accomplishments'
INFO 2025-06-06 17:53:44,121 search_chatbot 84935 6194688000 Message count: 1, Is summarizing: False
INFO 2025-06-06 17:53:44,122 basehttp 84935 6194688000 "POST /gobeyond/search/chat/ HTTP/1.1" 200 5172
INFO 2025-06-06 17:56:33,785 autoreload 84935 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 17:56:37,316 autoreload 85997 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 17:56:49,145 autoreload 85997 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 17:56:50,148 autoreload 86056 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 17:56:57,562 autoreload 86056 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 17:56:58,392 autoreload 86114 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 17:57:09,303 autoreload 86114 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 17:57:10,199 autoreload 86179 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 17:57:20,904 autoreload 86179 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 17:57:21,822 autoreload 86248 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 17:57:32,521 autoreload 86248 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/search_chatbot.py changed, reloading.
INFO 2025-06-06 17:57:33,497 autoreload 86305 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 18:26:43,291 autoreload 86305 8619180608 /Users/<USER>/Library/CloudStorage/<EMAIL>/My Drive/Github/pisakhov-dot-com/site/pisakhov/gobeyond/services/embedding_service.py changed, reloading.
INFO 2025-06-06 18:26:44,583 autoreload 92655 8619180608 Watching for file changes with StatReloader
INFO 2025-06-06 18:27:29,229 search_chatbot 92863 8619180608 Started new search session d82f3e06-4cc0-4b2b-b05e-0ae706c626e9 for user radi
INFO 2025-06-06 18:27:29,894 search_chatbot 92863 8619180608 Router decision: needs_search=False, query=''
INFO 2025-06-06 18:27:30,387 search_chatbot 92863 8619180608 Message count: 1, Is summarizing: False
INFO 2025-06-06 18:27:30,904 search_chatbot 92863 8619180608 Router decision: needs_search=True, query='communication accomplishments'
INFO 2025-06-06 18:27:30,906 embedding_service 92863 8619180608 Loading Google Generative AI embedding model: models/embedding-001
INFO 2025-06-06 18:27:30,946 embedding_service 92863 8619180608 Google embedding model loaded successfully
INFO 2025-06-06 18:27:40,754 search_chatbot 92863 8619180608 Found 8 results for 'communication accomplishments'
INFO 2025-06-06 18:27:40,755 search_chatbot 92863 8619180608 Message count: 2, Is summarizing: False
INFO 2025-06-06 18:27:55,533 basehttp 92655 6191165440 "GET /gobeyond/search/ HTTP/1.1" 200 55529
INFO 2025-06-06 18:27:56,592 search_chatbot 92655 6191165440 Started new search session 56deb99c-4def-4ee9-87a0-cd2c3ac4aee0 for user radi
INFO 2025-06-06 18:27:56,595 basehttp 92655 6191165440 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 18:27:56,753 basehttp 92655 6207991808 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 324
INFO 2025-06-06 18:28:48,027 search_chatbot 92655 6191165440 Router decision: needs_search=False, query=''
INFO 2025-06-06 18:28:48,421 search_chatbot 92655 6191165440 Message count: 1, Is summarizing: False
INFO 2025-06-06 18:28:48,422 basehttp 92655 6191165440 "POST /gobeyond/search/chat/ HTTP/1.1" 200 213
INFO 2025-06-06 18:28:57,270 search_chatbot 92655 6191165440 Router decision: needs_search=True, query='resume ideas'
INFO 2025-06-06 18:28:57,272 embedding_service 92655 6191165440 Loading Google Generative AI embedding model: models/embedding-001
INFO 2025-06-06 18:28:57,329 embedding_service 92655 6191165440 Google embedding model loaded successfully
INFO 2025-06-06 18:29:08,830 search_chatbot 92655 6191165440 Found 8 results for 'resume ideas'
INFO 2025-06-06 18:29:08,833 search_chatbot 92655 6191165440 Message count: 2, Is summarizing: False
INFO 2025-06-06 18:29:08,835 basehttp 92655 6191165440 "POST /gobeyond/search/chat/ HTTP/1.1" 200 4715
INFO 2025-06-06 18:30:34,472 search_chatbot 92655 6191165440 Started new search session bb533273-e1c6-46f2-a396-ab1d2022717b for user radi
INFO 2025-06-06 18:30:34,473 basehttp 92655 6191165440 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 18:31:30,040 basehttp 92655 6207991808 "GET /gobeyond/search/ HTTP/1.1" 200 54731
INFO 2025-06-06 18:31:30,924 search_chatbot 92655 6224818176 Started new search session 6e1dcae9-80ed-4874-8cb7-326d13a0267b for user radi
INFO 2025-06-06 18:31:30,927 basehttp 92655 6224818176 "POST /gobeyond/search/start-session/ HTTP/1.1" 200 121
INFO 2025-06-06 18:31:30,950 basehttp 92655 6207991808 "GET /gobeyond/search/embedding-stats/ HTTP/1.1" 200 324
INFO 2025-06-06 18:40:30,902 basehttp 92655 6248525824 "GET / HTTP/1.1" 200 32374
INFO 2025-06-06 18:40:33,190 basehttp 92655 6248525824 "GET /gobeyond/ HTTP/1.1" 200 135532
INFO 2025-06-06 18:40:33,222 basehttp 92655 6248525824 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-06 18:40:33,222 basehttp 92655 6265352192 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-06 18:40:33,236 basehttp 92655 6265352192 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-06 18:40:33,237 basehttp 92655 6248525824 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-06 18:40:33,243 basehttp 92655 6248525824 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-06 18:40:33,243 basehttp 92655 6265352192 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-09 13:08:09,425 autoreload 96854 8619180608 Watching for file changes with StatReloader
INFO 2025-06-09 13:08:12,318 basehttp 96854 6131871744 "GET / HTTP/1.1" 200 32374
INFO 2025-06-09 13:08:15,055 basehttp 96854 6131871744 "GET /gobeyond/ HTTP/1.1" 200 135532
INFO 2025-06-09 13:08:15,106 basehttp 96854 6131871744 "GET /static/logo.png HTTP/1.1" 200 383851
INFO 2025-06-09 13:08:15,115 basehttp 96854 6131871744 "GET /static/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-09 13:08:15,117 basehttp 96854 6131871744 "GET /static/js/dialog_manager.js HTTP/1.1" 304 0
INFO 2025-06-09 13:08:15,117 basehttp 96854 6148698112 "GET /static/js/goal_management.js HTTP/1.1" 304 0
INFO 2025-06-09 13:08:15,119 basehttp 96854 6165524480 "GET /static/js/media_handling.js HTTP/1.1" 304 0
INFO 2025-06-09 13:08:15,120 basehttp 96854 6148698112 "GET /static/js/milestone_tracker.js HTTP/1.1" 304 0
INFO 2025-06-09 13:08:15,120 basehttp 96854 6131871744 "GET /static/js/add_record.js HTTP/1.1" 304 0
INFO 2025-06-09 13:12:33,152 autoreload 99762 8619180608 Watching for file changes with StatReloader
INFO 2025-06-09 13:12:35,048 basehttp 99762 6197981184 "GET /gobeyond/?archived=true HTTP/1.1" 200 281991
